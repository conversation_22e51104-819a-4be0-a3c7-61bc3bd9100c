(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7ac263aa"],{"3eba":function(t,e,a){"use strict";var n=a("aa74");a.d(e,"a",(function(){return n["a"]})),a.d(e,"b",(function(){return n["b"]})),a.d(e,"c",(function(){return n["c"]})),a.d(e,"d",(function(){return n["d"]})),a.d(e,"e",(function(){return n["e"]})),a.d(e,"f",(function(){return n["f"]})),a.d(e,"g",(function(){return n["h"]})),a.d(e,"h",(function(){return n["i"]})),a.d(e,"i",(function(){return n["q"]})),a.d(e,"j",(function(){return n["t"]})),a.d(e,"k",(function(){return n["u"]})),a.d(e,"l",(function(){return n["v"]})),a.d(e,"m",(function(){return n["y"]})),a.d(e,"n",(function(){return n["z"]})),a.d(e,"o",(function(){return n["A"]})),a.d(e,"p",(function(){return n["C"]})),a.d(e,"q",(function(){return n["E"]})),a.d(e,"r",(function(){return n["F"]})),a.d(e,"s",(function(){return n["G"]})),a.d(e,"t",(function(){return n["J"]})),a.d(e,"u",(function(){return n["N"]})),a.d(e,"v",(function(){return n["O"]})),a.d(e,"w",(function(){return n["P"]})),a.d(e,"x",(function(){return n["Z"]})),a.d(e,"y",(function(){return n["ab"]})),a.d(e,"z",(function(){return n["eb"]}));var i=a("22b4"),r=(a("1be7"),a("f95e")),o=a("5e81"),l=a("ee29");Object(i["a"])([r["a"],o["a"]]);Object(i["a"])(l["a"])},"87a1":function(t,e,a){"use strict";var n=a("3eba");n["j"]({type:"series.wordCloud",visualStyleAccessPath:"textStyle",visualStyleMapper:function(t){return{fill:t.get("color")}},visualDrawType:"fill",optionUpdated:function(){var t=this.option;t.gridSize=Math.max(Math.floor(t.gridSize),4)},getInitialData:function(t,e){var a=n["o"].createDimensions(t.data,{coordDimensions:["value"]}),i=new n["e"](a,this);return i.initData(t.data),i},defaultOption:{maskImage:null,shape:"circle",keepAspect:!1,left:"center",top:"center",width:"70%",height:"80%",sizeRange:[12,60],rotationRange:[-90,90],rotationStep:45,gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,textStyle:{fontWeight:"normal"}}}),n["i"]({type:"wordCloud",render:function(t,e,a){var i=this.group;i.removeAll();var r=t.getData(),o=t.get("gridSize");t.layoutInstance.ondraw=function(e,a,l,s){var d=r.getItemModel(l),u=d.getModel("textStyle"),c=new n["n"].Text({style:n["o"].createTextStyle(u),scaleX:1/s.info.mu,scaleY:1/s.info.mu,x:(s.gx+s.info.gw/2)*o,y:(s.gy+s.info.gh/2)*o,rotation:s.rot});c.setStyle({x:s.info.fillTextOffsetX,y:s.info.fillTextOffsetY+.5*a,text:e,verticalAlign:"middle",fill:r.getItemVisual(l,"style").fill,fontSize:a}),i.add(c),r.setItemGraphicEl(l,c),c.ensureState("emphasis").style=n["o"].createTextStyle(d.getModel(["emphasis","textStyle"]),{state:"emphasis"}),c.ensureState("blur").style=n["o"].createTextStyle(d.getModel(["blur","textStyle"]),{state:"blur"}),n["o"].enableHoverEmphasis(c,d.get(["emphasis","focus"]),d.get(["emphasis","blurScope"])),c.stateTransition={duration:t.get("animation")?t.get(["stateAnimation","duration"]):0,easing:t.get(["stateAnimation","easing"])},c.__highDownDispatcher=!0},this._model=t},remove:function(){this.group.removeAll(),this._model.layoutInstance.dispose()},dispose:function(){this._model.layoutInstance.dispose()}}),
/*!
 * wordcloud2.js
 * http://timdream.org/wordcloud2.js/
 *
 * Copyright 2011 - 2019 Tim Guan-tin Chien and contributors.
 * Released under the MIT license
 */
window.setImmediate||(window.setImmediate=function(){return window.msSetImmediate||window.webkitSetImmediate||window.mozSetImmediate||window.oSetImmediate||function(){if(!window.postMessage||!window.addEventListener)return null;var t=[void 0],e="zero-timeout-message",a=function(a){var n=t.length;return t.push(a),window.postMessage(e+n.toString(36),"*"),n};return window.addEventListener("message",(function(a){if("string"===typeof a.data&&a.data.substr(0,e.length)===e){a.stopImmediatePropagation();var n=parseInt(a.data.substr(e.length),36);t[n]&&(t[n](),t[n]=void 0)}}),!0),window.clearImmediate=function(e){t[e]&&(t[e]=void 0)},a}()||function(t){window.setTimeout(t,0)}}()),window.clearImmediate||(window.clearImmediate=function(){return window.msClearImmediate||window.webkitClearImmediate||window.mozClearImmediate||window.oClearImmediate||function(t){window.clearTimeout(t)}}());var i=function(){var t=document.createElement("canvas");if(!t||!t.getContext)return!1;var e=t.getContext("2d");return!!e&&(!!e.getImageData&&(!!e.fillText&&(!!Array.prototype.some&&!!Array.prototype.push)))}(),r=function(){if(i){var t,e,a=document.createElement("canvas").getContext("2d"),n=20;while(n){if(a.font=n.toString(10)+"px sans-serif",a.measureText("Ｗ").width===t&&a.measureText("m").width===e)return n+1;t=a.measureText("Ｗ").width,e=a.measureText("m").width,n--}return 0}}(),o=function(t){if(Array.isArray(t)){var e=t.slice();return e.splice(0,2),e}return[]},l=function(t){for(var e,a,n=t.length;n;)e=Math.floor(Math.random()*n),a=t[--n],t[n]=t[e],t[e]=a;return t},s={},d=function(t,e){if(i){var a=Math.floor(Math.random()*Date.now());Array.isArray(t)||(t=[t]),t.forEach((function(e,a){if("string"===typeof e){if(t[a]=document.getElementById(e),!t[a])throw new Error("The element id specified is not found.")}else if(!e.tagName&&!e.appendChild)throw new Error("You must pass valid HTML elements, or ID of the element.")}));var n={list:[],fontFamily:'"Trebuchet MS", "Heiti TC", "微軟正黑體", "Arial Unicode MS", "Droid Fallback Sans", sans-serif',fontWeight:"normal",color:"random-dark",minSize:0,weightFactor:1,clearCanvas:!0,backgroundColor:"#fff",gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,origin:null,drawMask:!1,maskColor:"rgba(255,0,0,0.3)",maskGapWidth:.3,layoutAnimation:!0,wait:0,abortThreshold:0,abort:function(){},minRotation:-Math.PI/2,maxRotation:Math.PI/2,rotationStep:.1,shuffle:!0,rotateRatio:.1,shape:"circle",ellipticity:.65,classes:null,hover:null,click:null};if(e)for(var d in e)d in n&&(n[d]=e[d]);if("function"!==typeof n.weightFactor){var u=n.weightFactor;n.weightFactor=function(t){return t*u}}if("function"!==typeof n.shape)switch(n.shape){case"circle":default:n.shape="circle";break;case"cardioid":n.shape=function(t){return 1-Math.sin(t)};break;case"diamond":n.shape=function(t){var e=t%(2*Math.PI/4);return 1/(Math.cos(e)+Math.sin(e))};break;case"square":n.shape=function(t){return Math.min(1/Math.abs(Math.cos(t)),1/Math.abs(Math.sin(t)))};break;case"triangle-forward":n.shape=function(t){var e=t%(2*Math.PI/3);return 1/(Math.cos(e)+Math.sqrt(3)*Math.sin(e))};break;case"triangle":case"triangle-upright":n.shape=function(t){var e=(t+3*Math.PI/2)%(2*Math.PI/3);return 1/(Math.cos(e)+Math.sqrt(3)*Math.sin(e))};break;case"pentagon":n.shape=function(t){var e=(t+.955)%(2*Math.PI/5);return 1/(Math.cos(e)+.726543*Math.sin(e))};break;case"star":n.shape=function(t){var e=(t+.955)%(2*Math.PI/10);return(t+.955)%(2*Math.PI/5)-2*Math.PI/10>=0?1/(Math.cos(2*Math.PI/10-e)+3.07768*Math.sin(2*Math.PI/10-e)):1/(Math.cos(e)+3.07768*Math.sin(e))};break}n.gridSize=Math.max(Math.floor(n.gridSize),4);var c,f,h,g,m,w,v,p,y=n.gridSize,x=y-n.maskGapWidth,M=Math.abs(n.maxRotation-n.minRotation),b=Math.min(n.maxRotation,n.minRotation),S=n.rotationStep;switch(n.color){case"random-dark":v=function(){return _(10,50)};break;case"random-light":v=function(){return _(50,90)};break;default:"function"===typeof n.color&&(v=n.color);break}"function"===typeof n.fontWeight&&(p=n.fontWeight);var I=null;"function"===typeof n.classes&&(I=n.classes);var k,T=!1,C=[],E=function(t){var e,a,n=t.currentTarget,i=n.getBoundingClientRect();t.touches?(e=t.touches[0].clientX,a=t.touches[0].clientY):(e=t.clientX,a=t.clientY);var r=e-i.left,o=a-i.top,l=Math.floor(r*(n.width/i.width||1)/y),s=Math.floor(o*(n.height/i.height||1)/y);return C[l]?C[l][s]:null},A=function(t){var e=E(t);k!==e&&(k=e,e?n.hover(e.item,e.dimension,t):n.hover(void 0,void 0,t))},O=function(t){var e=E(t);e&&(n.click(e.item,e.dimension,t),t.preventDefault())},F=[],R=function(t){if(F[t])return F[t];var e=8*t,a=e,i=[];0===t&&i.push([g[0],g[1],0]);while(a--){var r=1;"circle"!==n.shape&&(r=n.shape(a/e*2*Math.PI)),i.push([g[0]+t*r*Math.cos(-a/e*2*Math.PI),g[1]+t*r*Math.sin(-a/e*2*Math.PI)*n.ellipticity,a/e*2*Math.PI])}return F[t]=i,i},D=function(){return n.abortThreshold>0&&(new Date).getTime()-w>n.abortThreshold},z=function(){return 0===n.rotateRatio||Math.random()>n.rotateRatio?0:0===M?b:b+Math.round(Math.random()*M/S)*S},P=function(t,e,a,i){var o=!1,l=n.weightFactor(e);if(l<=n.minSize)return!1;var s,d=1;l<r&&(d=function(){var t=2;while(t*l<r)t+=2;return t}()),s=p?p(t,e,l,i):n.fontWeight;var u=document.createElement("canvas"),c=u.getContext("2d",{willReadFrequently:!0});c.font=s+" "+(l*d).toString(10)+"px "+n.fontFamily;var f=c.measureText(t).width/d,h=Math.max(l*d,c.measureText("m").width,c.measureText("Ｗ").width)/d,g=f+2*h,m=3*h,w=Math.ceil(g/y),v=Math.ceil(m/y);g=w*y,m=v*y;var x=-f/2,M=.4*-h,b=Math.ceil((g*Math.abs(Math.sin(a))+m*Math.abs(Math.cos(a)))/y),S=Math.ceil((g*Math.abs(Math.cos(a))+m*Math.abs(Math.sin(a)))/y),I=S*y,k=b*y;u.setAttribute("width",I),u.setAttribute("height",k),o&&(document.body.appendChild(u),c.save()),c.scale(1/d,1/d),c.translate(I*d/2,k*d/2),c.rotate(-a),c.font=s+" "+(l*d).toString(10)+"px "+n.fontFamily,c.fillStyle="#000",c.textBaseline="middle",c.fillText(t,x*d,(M+.5*l)*d);var T=c.getImageData(0,0,I,k).data;if(D())return!1;o&&(c.strokeRect(x*d,M,f*d,h*d),c.restore());var C,E,A,O=[],F=S,R=[b/2,S/2,b/2,S/2];while(F--){C=b;while(C--){A=y;t:while(A--){E=y;while(E--)if(T[4*((C*y+A)*I+(F*y+E))+3]){O.push([F,C]),F<R[3]&&(R[3]=F),F>R[1]&&(R[1]=F),C<R[0]&&(R[0]=C),C>R[2]&&(R[2]=C),o&&(c.fillStyle="rgba(255, 0, 0, 0.5)",c.fillRect(F*y,C*y,y-.5,y-.5));break t}}o&&(c.fillStyle="rgba(0, 0, 255, 0.5)",c.fillRect(F*y,C*y,y-.5,y-.5))}}return o&&(c.fillStyle="rgba(0, 255, 0, 0.5)",c.fillRect(R[3]*y,R[0]*y,(R[1]-R[3]+1)*y,(R[2]-R[0]+1)*y)),{mu:d,occupied:O,bounds:R,gw:S,gh:b,fillTextOffsetX:x,fillTextOffsetY:M,fillTextWidth:f,fillTextHeight:h,fontSize:l}},L=function(t,e,a,i,r){var o=r.length;while(o--){var l=t+r[o][0],s=e+r[o][1];if(l>=f||s>=h||l<0||s<0){if(!n.drawOutOfBound)return!1}else if(!c[l][s])return!1}return!0},W=function(e,a,i,r,o,l,s,d,u,c){var f,h,g,m=i.fontSize;f=v?v(r,o,m,l,s,c):n.color,h=p?p(r,o,m,c):n.fontWeight,g=I?I(r,o,m,c):n.classes,t.forEach((function(t){if(t.getContext){var o=t.getContext("2d"),l=i.mu;o.save(),o.scale(1/l,1/l),o.font=h+" "+(m*l).toString(10)+"px "+n.fontFamily,o.fillStyle=f,o.translate((e+i.gw/2)*y*l,(a+i.gh/2)*y*l),0!==d&&o.rotate(-d),o.textBaseline="middle",o.fillText(r,i.fillTextOffsetX*l,(i.fillTextOffsetY+.5*m)*l),o.restore()}else{var s=document.createElement("span"),c="";c="rotate("+-d/Math.PI*180+"deg) ",1!==i.mu&&(c+="translateX(-"+i.fillTextWidth/4+"px) scale("+1/i.mu+")");var w={position:"absolute",display:"block",font:h+" "+m*i.mu+"px "+n.fontFamily,left:(e+i.gw/2)*y+i.fillTextOffsetX+"px",top:(a+i.gh/2)*y+i.fillTextOffsetY+"px",width:i.fillTextWidth+"px",height:i.fillTextHeight+"px",lineHeight:m+"px",whiteSpace:"nowrap",transform:c,webkitTransform:c,msTransform:c,transformOrigin:"50% 40%",webkitTransformOrigin:"50% 40%",msTransformOrigin:"50% 40%"};for(var v in f&&(w.color=f),s.textContent=r,w)s.style[v]=w[v];if(u)for(var p in u)s.setAttribute(p,u[p]);g&&(s.className+=g),t.appendChild(s)}}))},B=function(e,a,n,i,r){if(!(e>=f||a>=h||e<0||a<0)){if(c[e][a]=!1,n){var o=t[0].getContext("2d");o.fillRect(e*y,a*y,x,x)}T&&(C[e][a]={item:r,dimension:i})}},X=function(e,a,i,r,o,l){var s,d,u=o.occupied,c=n.drawMask;if(c&&(s=t[0].getContext("2d"),s.save(),s.fillStyle=n.maskColor),T){var g=o.bounds;d={x:(e+g[3])*y,y:(a+g[0])*y,w:(g[1]-g[3]+1)*y,h:(g[2]-g[0]+1)*y}}var m=u.length;while(m--){var w=e+u[m][0],v=a+u[m][1];w>=f||v>=h||w<0||v<0||B(w,v,c,d,l)}c&&s.restore()},Y=function t(e,a){if(a>20)return null;var i,r,s;Array.isArray(e)?(i=e[0],r=e[1]):(i=e.word,r=e.weight,s=e.attributes);var d=z(),u=o(e),c=P(i,r,d,u);if(!c)return!1;if(D())return!1;if(!n.drawOutOfBound&&!n.shrinkToFit){var g=c.bounds;if(g[1]-g[3]+1>f||g[2]-g[0]+1>h)return!1}var w=m+1,v=function(t){var a=Math.floor(t[0]-c.gw/2),n=Math.floor(t[1]-c.gh/2),o=c.gw,l=c.gh;return!!L(a,n,o,l,c.occupied)&&(W(a,n,c,i,r,m-w,t[2],d,s,u),X(a,n,o,l,c,e),{gx:a,gy:n,rot:d,info:c})};while(w--){var p=R(m-w);n.shuffle&&(p=[].concat(p),l(p));for(var y=0;y<p.length;y++){var x=v(p[y]);if(x)return x}}return n.shrinkToFit?(Array.isArray(e)?e[1]=3*e[1]/4:e.weight=3*e.weight/4,t(e,a+1)):null},q=function(e,a,n){if(a)return!t.some((function(t){var a=new CustomEvent(e,{detail:n||{}});return!t.dispatchEvent(a)}),this);t.forEach((function(t){var a=new CustomEvent(e,{detail:n||{}});t.dispatchEvent(a)}),this)},H=function(){var e=t[0];if(e.getContext)f=Math.ceil(e.width/y),h=Math.ceil(e.height/y);else{var i=e.getBoundingClientRect();f=Math.ceil(i.width/y),h=Math.ceil(i.height/y)}if(q("wordcloudstart",!0)){var r,o,l,d,u;if(g=n.origin?[n.origin[0]/y,n.origin[1]/y]:[f/2,h/2],m=Math.floor(Math.sqrt(f*f+h*h)),c=[],!e.getContext||n.clearCanvas){t.forEach((function(t){if(t.getContext){var e=t.getContext("2d");e.fillStyle=n.backgroundColor,e.clearRect(0,0,f*(y+1),h*(y+1)),e.fillRect(0,0,f*(y+1),h*(y+1))}else t.textContent="",t.style.backgroundColor=n.backgroundColor,t.style.position="relative"})),r=f;while(r--){c[r]=[],o=h;while(o--)c[r][o]=!0}}else{var v=document.createElement("canvas").getContext("2d");v.fillStyle=n.backgroundColor,v.fillRect(0,0,1,1);var p,x,M=v.getImageData(0,0,1,1).data,b=e.getContext("2d").getImageData(0,0,f*y,h*y).data;r=f;while(r--){c[r]=[],o=h;while(o--){x=y;t:while(x--){p=y;while(p--){l=4;while(l--)if(b[4*((o*y+x)*f*y+(r*y+p))+l]!==M[l]){c[r][o]=!1;break t}}}!1!==c[r][o]&&(c[r][o]=!0)}}b=v=M=void 0}if(n.hover||n.click){T=!0,r=f+1;while(r--)C[r]=[];n.hover&&e.addEventListener("mousemove",A),n.click&&(e.addEventListener("click",O),e.addEventListener("touchstart",O),e.addEventListener("touchend",(function(t){t.preventDefault()})),e.style.webkitTapHighlightColor="rgba(0, 0, 0, 0)"),e.addEventListener("wordcloudstart",(function t(){e.removeEventListener("wordcloudstart",t),e.removeEventListener("mousemove",A),e.removeEventListener("click",O),k=void 0}))}l=0;var S=!0;n.layoutAnimation?0!==n.wait?(d=window.setTimeout,u=window.clearTimeout):(d=window.setImmediate,u=window.clearImmediate):(d=function(t){t()},u=function(){S=!1});var I=function(e,a){t.forEach((function(t){t.addEventListener(e,a)}),this)},E=function(e,a){t.forEach((function(t){t.removeEventListener(e,a)}),this)},F=function t(){E("wordcloudstart",t),u(s[a])};I("wordcloudstart",F),s[a]=(n.layoutAnimation?d:setTimeout)((function t(){if(S){if(l>=n.list.length)return u(s[a]),q("wordcloudstop",!1),E("wordcloudstart",F),void delete s[a];w=(new Date).getTime();var e=Y(n.list[l],0),i=!q("wordclouddrawn",!0,{item:n.list[l],drawn:e});if(D()||i)return u(s[a]),n.abort(),q("wordcloudabort",!1),q("wordcloudstop",!1),void E("wordcloudstart",F);l++,s[a]=d(t,n.wait)}}),n.wait)}};H()}function _(t,e){return"hsl("+(360*Math.random()).toFixed()+","+(30*Math.random()+70).toFixed()+"%,"+(Math.random()*(e-t)+t).toFixed()+"%)"}};d.isSupported=i,d.minFontSize=r;var u=d;if(!u.isSupported)throw new Error("Sorry your browser not support wordCloud");function c(t){for(var e=t.getContext("2d"),a=e.getImageData(0,0,t.width,t.height),n=e.createImageData(a),i=0,r=0,o=0;o<a.data.length;o+=4){var l=a.data[o+3];if(l>128){var s=a.data[o]+a.data[o+1]+a.data[o+2];i+=s,++r}}var d=i/r;for(o=0;o<a.data.length;o+=4){s=a.data[o]+a.data[o+1]+a.data[o+2],l=a.data[o+3];l<128||s>d?(n.data[o]=0,n.data[o+1]=0,n.data[o+2]=0,n.data[o+3]=0):(n.data[o]=255,n.data[o+1]=255,n.data[o+2]=255,n.data[o+3]=255)}e.putImageData(n,0,0)}function f(t,e){var a=t.width,n=t.height;a>n*e?(t.x+=(a-n*e)/2,t.width=n*e):(t.y+=(n-a/e)/2,t.height=a/e)}n["t"]((function(t,e){t.eachSeriesByType("wordCloud",(function(a){var i=n["o"].getLayoutRect(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()}),r=a.get("keepAspect"),o=a.get("maskImage"),l=o?o.width/o.height:1;r&&f(i,l);var s=a.getData(),d=document.createElement("canvas");d.width=i.width,d.height=i.height;var h=d.getContext("2d");if(o)try{h.drawImage(o,0,0,d.width,d.height),c(d)}catch(x){console.error("Invalid mask image"),console.error(x.toString())}var g=a.get("sizeRange"),m=a.get("rotationRange"),w=s.getDataExtent("value"),v=Math.PI/180,p=a.get("gridSize");function y(t){var e=t.detail.item;t.detail.drawn&&a.layoutInstance.ondraw&&(t.detail.drawn.gx+=i.x/p,t.detail.drawn.gy+=i.y/p,a.layoutInstance.ondraw(e[0],e[1],e[2],t.detail.drawn))}u(d,{list:s.mapArray("value",(function(t,e){var a=s.getItemModel(e);return[s.getName(e),a.get("textStyle.fontSize",!0)||n["q"].linearMap(t,w,g),e]})).sort((function(t,e){return e[1]-t[1]})),fontFamily:a.get("textStyle.fontFamily")||a.get("emphasis.textStyle.fontFamily")||t.get("textStyle.fontFamily"),fontWeight:a.get("textStyle.fontWeight")||a.get("emphasis.textStyle.fontWeight")||t.get("textStyle.fontWeight"),gridSize:p,ellipticity:i.height/i.width,minRotation:m[0]*v,maxRotation:m[1]*v,clearCanvas:!o,rotateRatio:1,rotationStep:a.get("rotationStep")*v,drawOutOfBound:a.get("drawOutOfBound"),shrinkToFit:a.get("shrinkToFit"),layoutAnimation:a.get("layoutAnimation"),shuffle:!1,shape:a.get("shape")}),d.addEventListener("wordclouddrawn",y),a.layoutInstance&&a.layoutInstance.dispose(),a.layoutInstance={ondraw:null,dispose:function(){d.removeEventListener("wordclouddrawn",y),d.addEventListener("wordclouddrawn",(function(t){t.preventDefault()}))}}}))})),n["w"]((function(t){var e=(t||{}).series;!n["y"].isArray(e)&&(e=e?[e]:[]);var a=["shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function i(t){t&&n["y"].each(a,(function(e){t.hasOwnProperty(e)&&(t["text"+n["k"].capitalFirst(e)]=t[e])}))}n["y"].each(e,(function(t){if(t&&"wordCloud"===t.type){var e=t.textStyle||{};i(e.normal),i(e.emphasis)}}))}))}}]);