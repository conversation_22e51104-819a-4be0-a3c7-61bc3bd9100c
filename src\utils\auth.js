import Cookies from 'js-cookie'

const To<PERSON><PERSON>ey = 'Admin-Token-boryou'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  console.log('process.env.ENV', process.env.ENV)
  if (process.env.ENV === 'production') {
    return Cookies.set(To<PERSON><PERSON>ey, {
      sameSite: 'None',
      secure: true // 必须启用HTTPS
    })
  }else{
    return Cookies.set(TokenKey)
  }
}

export function removeToken() {
  if (process.env.ENV === 'production') {
    return Cookies.remove(To<PERSON><PERSON>ey, {
      sameSite: 'None',
      secure: true // 必须启用HTTPS
    })
  }else{
    return Cookies.remove(TokenKey)
  }
}
