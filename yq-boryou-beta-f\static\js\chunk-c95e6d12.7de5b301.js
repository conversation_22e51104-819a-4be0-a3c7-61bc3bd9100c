(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c95e6d12"],{"26fc":function(t,s,a){t.exports=a.p+"static/img/404_cloud.0f4bc32b.png"},2754:function(t,s,a){"use strict";a.r(s);var i=function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"wscn-http404-container"},[a("div",{staticClass:"wscn-http404"},[t._m(0),a("div",{staticClass:"bullshit"},[a("div",{staticClass:"bullshit__oops"},[t._v(" 404错误! ")]),a("div",{staticClass:"bullshit__headline"},[t._v(" "+t._s(t.message)+" ")]),a("div",{staticClass:"bullshit__info"},[t._v(" 对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。 ")]),a("router-link",{staticClass:"bullshit__return-home",attrs:{to:t.homePath}},[t._v(" 返回首页 ")])],1)])])},c=[function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{staticClass:"pic-404"},[i("img",{staticClass:"pic-404__parent",attrs:{src:a("a36b"),alt:"404"}}),i("img",{staticClass:"pic-404__child left",attrs:{src:a("26fc"),alt:"404"}}),i("img",{staticClass:"pic-404__child mid",attrs:{src:a("26fc"),alt:"404"}}),i("img",{staticClass:"pic-404__child right",attrs:{src:a("26fc"),alt:"404"}})])}],e={name:"Page404",computed:{message:function(){return"找不到网页！"}},data:function(){return{homePath:""}},mounted:function(){var t=this;this.$store.dispatch("GetRouters").then((function(){var s=t.$store.state.user.newPath;console.log("homePath :>> ",t.$store.state.user.newPath),t.homePath="/".concat(s)}))}},n=e,l=(a("d256"),a("2877")),r=Object(l["a"])(n,i,c,!1,null,"a4f4a9f6",null);s["default"]=r.exports},a36b:function(t,s,a){t.exports=a.p+"static/img/404.a57b6f31.png"},d256:function(t,s,a){"use strict";a("ecdd")},ecdd:function(t,s,a){}}]);