<!DOCTYPE html><html><head><meta charset=utf-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=renderer content=webkit><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon href="" id=dynamic-favicon><title>舆情监测系统</title><meta property=og:title content=舆情监测系统><!--[if lt IE 11]><script>window.location.href='html/ie.html';</script><![endif]--><style>html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #e9f8fa;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }

    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      /* font-family: 'Open Sans'; */
      color: rgb(95, 140, 231);
      font-size: 20px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 50%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #fff;
      opacity: 0.5;
    }</style><link href=/static/css/chunk-libs.b57e4bfb.css rel=stylesheet><link href=/static/css/app.46e4c5f2.css rel=stylesheet></head><body><script src=/tagcanvas.min.js></script><div id=app><div id=loader-wrapper><div class="loader-section section-left"></div><div class="loader-section section-right"></div><div class=load_title>Loading ...</div></div></div><script>(function(c){function e(e){for(var u,a,f=e[0],t=e[1],r=e[2],k=0,o=[];k<f.length;k++)a=f[k],Object.prototype.hasOwnProperty.call(d,a)&&d[a]&&o.push(d[a][0]),d[a]=0;for(u in t)Object.prototype.hasOwnProperty.call(t,u)&&(c[u]=t[u]);b&&b(e);while(o.length)o.shift()();return h.push.apply(h,r||[]),n()}function n(){for(var c,e=0;e<h.length;e++){for(var n=h[e],u=!0,a=1;a<n.length;a++){var f=n[a];0!==d[f]&&(u=!1)}u&&(h.splice(e--,1),c=t(t.s=n[0]))}return c}var u={},a={runtime:0},d={runtime:0},h=[];function f(c){return t.p+"static/js/"+({"chunk-commons":"chunk-commons"}[c]||c)+"."+{"chunk-07dcd4fa":"b2978174","chunk-15090bf1":"423e3060","chunk-c64da886":"2370495a","chunk-1882fba8":"4d3974b7","chunk-18a533e8":"9b2198ff","chunk-1cd0d916":"bec8c916","chunk-208d53c4":"69f5bf27","chunk-5ecb56a0":"f10630d2","chunk-11410e0e":"742fd92d","chunk-2d0a2db2":"05db405d","chunk-2d0ac3da":"fde891d8","chunk-2d0c7b50":"4e16fa63","chunk-2d0e2366":"9b139797","chunk-67961041":"4d3eb2ab","chunk-2d0f012d":"ddeb06bb","chunk-2d212b99":"eadf8bea","chunk-1b925f0a":"92fe6cec","chunk-ead28086":"92034dda","chunk-30ba103a":"d7c15ac1","chunk-5545fd80":"f66ffd44","chunk-5bb73842":"5f889adb","chunk-714c1298":"cb830eeb","chunk-3756f77f":"91a1da7a","chunk-6c992b88":"0bc49d25","chunk-763e1101":"4f30d34a","chunk-44e7b4d3":"01ff9ee7","chunk-6f3a5038":"05a7ea07","chunk-7ac263aa":"e4fbcefe","chunk-24a65c0b":"b66e0612","chunk-f9ce9294":"95970fbd","chunk-55adf891":"e41470bb","chunk-57e94a84":"a43b14db","chunk-4b217d3a":"5ad8d0a7","chunk-54d8c200":"729bc3de","chunk-78a89526":"d1b43655","chunk-101428e4":"3a6c4d17","chunk-c95e6d12":"7de5b301","chunk-commons":"91a622cc","chunk-637f8d84":"49ea1986","chunk-37fb4878":"ccde6ac8","chunk-2e98ed84":"df021195","chunk-e28d6b1a":"9284015f","chunk-7fbfcfee":"e7510924","chunk-6c0ff4d2":"5518f842","chunk-628008fa":"f8096887","chunk-a090527e":"a609ad69","chunk-b485ea14":"7ef8e858","chunk-1c3d577c":"182eed48","chunk-dc136e2c":"16a4e3e2","chunk-d19c1a98":"8a849367","chunk-e94f93c0":"ef8c2fa0"}[c]+".js"}function t(e){if(u[e])return u[e].exports;var n=u[e]={i:e,l:!1,exports:{}};return c[e].call(n.exports,n,n.exports,t),n.l=!0,n.exports}t.e=function(c){var e=[],n={"chunk-07dcd4fa":1,"chunk-c64da886":1,"chunk-1882fba8":1,"chunk-18a533e8":1,"chunk-1cd0d916":1,"chunk-11410e0e":1,"chunk-67961041":1,"chunk-ead28086":1,"chunk-30ba103a":1,"chunk-5545fd80":1,"chunk-5bb73842":1,"chunk-3756f77f":1,"chunk-6c992b88":1,"chunk-44e7b4d3":1,"chunk-6f3a5038":1,"chunk-24a65c0b":1,"chunk-f9ce9294":1,"chunk-55adf891":1,"chunk-57e94a84":1,"chunk-4b217d3a":1,"chunk-54d8c200":1,"chunk-101428e4":1,"chunk-c95e6d12":1,"chunk-commons":1,"chunk-37fb4878":1,"chunk-2e98ed84":1,"chunk-e28d6b1a":1,"chunk-7fbfcfee":1,"chunk-628008fa":1,"chunk-a090527e":1,"chunk-b485ea14":1,"chunk-1c3d577c":1,"chunk-dc136e2c":1,"chunk-e94f93c0":1};a[c]?e.push(a[c]):0!==a[c]&&n[c]&&e.push(a[c]=new Promise((function(e,n){for(var u="static/css/"+({"chunk-commons":"chunk-commons"}[c]||c)+"."+{"chunk-07dcd4fa":"dcb0e8da","chunk-15090bf1":"31d6cfe0","chunk-c64da886":"f31de525","chunk-1882fba8":"41a32968","chunk-18a533e8":"31c65064","chunk-1cd0d916":"5d02ade1","chunk-208d53c4":"31d6cfe0","chunk-5ecb56a0":"31d6cfe0","chunk-11410e0e":"3c25f2a5","chunk-2d0a2db2":"31d6cfe0","chunk-2d0ac3da":"31d6cfe0","chunk-2d0c7b50":"31d6cfe0","chunk-2d0e2366":"31d6cfe0","chunk-67961041":"fd661bc3","chunk-2d0f012d":"31d6cfe0","chunk-2d212b99":"31d6cfe0","chunk-1b925f0a":"31d6cfe0","chunk-ead28086":"b6694b1b","chunk-30ba103a":"bf952d1c","chunk-5545fd80":"c2c87378","chunk-5bb73842":"84f98409","chunk-714c1298":"31d6cfe0","chunk-3756f77f":"5d3a4dae","chunk-6c992b88":"fb9bd69d","chunk-763e1101":"31d6cfe0","chunk-44e7b4d3":"11b383d6","chunk-6f3a5038":"20a11f13","chunk-7ac263aa":"31d6cfe0","chunk-24a65c0b":"d8379b85","chunk-f9ce9294":"707a8a96","chunk-55adf891":"8fa44406","chunk-57e94a84":"37b8ca45","chunk-4b217d3a":"17fc9ab6","chunk-54d8c200":"bc16dfe6","chunk-78a89526":"31d6cfe0","chunk-101428e4":"e5d88f2e","chunk-c95e6d12":"61704d3e","chunk-commons":"c9d6b5a0","chunk-637f8d84":"31d6cfe0","chunk-37fb4878":"893ebb1c","chunk-2e98ed84":"ea149205","chunk-e28d6b1a":"92c0f3c6","chunk-7fbfcfee":"71c3af1a","chunk-6c0ff4d2":"31d6cfe0","chunk-628008fa":"8670b93b","chunk-a090527e":"72298a49","chunk-b485ea14":"59878da5","chunk-1c3d577c":"90089c36","chunk-dc136e2c":"63b263a1","chunk-d19c1a98":"31d6cfe0","chunk-e94f93c0":"bc8d1b03"}[c]+".css",d=t.p+u,h=document.getElementsByTagName("link"),f=0;f<h.length;f++){var r=h[f],k=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(k===u||k===d))return e()}var o=document.getElementsByTagName("style");for(f=0;f<o.length;f++){r=o[f],k=r.getAttribute("data-href");if(k===u||k===d)return e()}var b=document.createElement("link");b.rel="stylesheet",b.type="text/css",b.onload=e,b.onerror=function(e){var u=e&&e.target&&e.target.src||d,h=new Error("Loading CSS chunk "+c+" failed.\n("+u+")");h.code="CSS_CHUNK_LOAD_FAILED",h.request=u,delete a[c],b.parentNode.removeChild(b),n(h)},b.href=d;var i=document.getElementsByTagName("head")[0];i.appendChild(b)})).then((function(){a[c]=0})));var u=d[c];if(0!==u)if(u)e.push(u[2]);else{var h=new Promise((function(e,n){u=d[c]=[e,n]}));e.push(u[2]=h);var r,k=document.createElement("script");k.charset="utf-8",k.timeout=120,t.nc&&k.setAttribute("nonce",t.nc),k.src=f(c);var o=new Error;r=function(e){k.onerror=k.onload=null,clearTimeout(b);var n=d[c];if(0!==n){if(n){var u=e&&("load"===e.type?"missing":e.type),a=e&&e.target&&e.target.src;o.message="Loading chunk "+c+" failed.\n("+u+": "+a+")",o.name="ChunkLoadError",o.type=u,o.request=a,n[1](o)}d[c]=void 0}};var b=setTimeout((function(){r({type:"timeout",target:k})}),12e4);k.onerror=k.onload=r,document.head.appendChild(k)}return Promise.all(e)},t.m=c,t.c=u,t.d=function(c,e,n){t.o(c,e)||Object.defineProperty(c,e,{enumerable:!0,get:n})},t.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},t.t=function(c,e){if(1&e&&(c=t(c)),8&e)return c;if(4&e&&"object"===typeof c&&c&&c.__esModule)return c;var n=Object.create(null);if(t.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:c}),2&e&&"string"!=typeof c)for(var u in c)t.d(n,u,function(e){return c[e]}.bind(null,u));return n},t.n=function(c){var e=c&&c.__esModule?function(){return c["default"]}:function(){return c};return t.d(e,"a",e),e},t.o=function(c,e){return Object.prototype.hasOwnProperty.call(c,e)},t.p="/",t.oe=function(c){throw console.error(c),c};var r=window["webpackJsonp"]=window["webpackJsonp"]||[],k=r.push.bind(r);r.push=e,r=r.slice();for(var o=0;o<r.length;o++)e(r[o]);var b=k;n()})([]);</script><script src=/static/js/chunk-elementUI.a099139a.js></script><script src=/static/js/chunk-libs.ddf357aa.js></script><script src=/static/js/app.9652d648.js></script></body></html>