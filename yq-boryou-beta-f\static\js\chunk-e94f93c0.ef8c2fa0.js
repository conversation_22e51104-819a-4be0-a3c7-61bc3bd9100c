(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e94f93c0"],{"750a":function(e,t,s){"use strict";s("dfa9")},"823d":function(e,t,s){e.exports=s.p+"static/img/screen1.1df75e99.jpg"},b952:function(e,t,s){e.exports=s.p+"static/img/screen2.62b79e51.jpg"},d66a:function(e,t,s){"use strict";s.r(t);var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"startupPage"},[n("div",{staticClass:"screenHtml"},[n("div",{staticClass:"screenTitle"},[e._v("舆情监测可视化系统")]),n("div",{staticClass:"screenMenu"},[n("ul",[n("li",[n("div",{staticClass:"screen-amplify"},[n("span",[e._v("舆情态势感知")]),n("div",{staticClass:"screenimg",on:{click:function(t){return e.handleScreen(1)}}},[n("img",{attrs:{src:s("823d"),alt:""}})])])]),n("li",[n("div",{staticClass:"screen-amplify",on:{click:function(t){return e.handleScreen(2)}}},[n("span",[e._v("数据采集态势")]),e._m(0)])])])])])])},a=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"screenimg"},[n("img",{attrs:{src:s("b952"),alt:""}})])}],c=(s("c077"),s("ed08"),{methods:{handleScreen:function(e){if(2==e){var t=this.$router.resolve({path:"/homeScreen/screenData"});window.open(t.href,"_blank")}else{var s=this.$router.resolve({path:"/homeScreen/screen"});window.open(s.href,"_blank")}}}}),i=c,r=(s("750a"),s("2877")),l=Object(r["a"])(i,n,a,!1,null,"1075efaa",null);t["default"]=l.exports},dfa9:function(e,t,s){}}]);