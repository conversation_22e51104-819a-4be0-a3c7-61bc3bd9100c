(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a090527e"],{"2a7c":function(e,t,n){},"3adc":function(e,t,n){"use strict";n("2a7c")},a3a9:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"vsConsolePage"},[n("VsScreen",{ref:"vsScreen",staticStyle:{height:"calc(100% - 0px)"},attrs:{drillTaskId:e.drillTaskId,"current-step":e.currentStep,"next-step":e.nextStep,newCurrentStage:e.newCurrentStage,detailInfo:e.detailInfo,showInstruction:!1,loading:e.vsScreenLoading}})],1)},a=[],s=n("c7eb"),c=n("1da1"),i=(n("4de4"),n("e9c4"),n("b64b"),n("d3b7"),n("0643"),n("2382"),n("986e")),d=n("265d"),o=n("f994"),l={components:{VsScreen:o["default"]},data:function(){return{currentStep:"",nextStep:"",newCurrentStage:{blueStageScore:"",redStageScore:"",scoreType:""},detailInfo:{},vsScreenLoading:!1}},computed:{drillTaskId:function(){var e;return(null===(e=this.$route.query)||void 0===e?void 0:e.drillTaskId)||""}},created:function(){this.unlistenMessage=Object(i["c"])(i["a"].MESSAGE,this.handleMessage)},beforeDestroy:function(){var e;null===(e=this.unlistenMessage)||void 0===e||e.call(this)},mounted:function(){var e;null!==(e=this.$route.query)&&void 0!==e&&e.drillTaskId?this.queryDetailInfo():this.$message.error("id不能为空")},methods:{queryDetailInfo:function(){var e=arguments,t=this;return Object(c["a"])(Object(s["a"])().mark((function n(){var r,a,c;return Object(s["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=!(e.length>0&&void 0!==e[0])||e[0],r&&(t.vsScreenLoading=!0),n.prev=2,n.next=5,Object(d["h"])({drillTaskId:t.drillTaskId});case 5:if(a=n.sent,"200"!=a.code){n.next=19;break}return console.log("detailInfo",a.data),t.detailInfo=a.data,t.currentStep=a.data.currentStageId,t.nextStep=a.data.nextStageId,c=a.data.drillProcessStageRes.filter((function(e){return e.processStageId==a.data.currentStageId})),c.length>0?t.newCurrentStage=JSON.parse(JSON.stringify(c))[0]:t.newCurrentStage={},console.log("currentStep1",t.currentStep,"nextStep1",t.nextStep,"newCurrentStage",t.newCurrentStage),n.next=16,t.$nextTick();case 16:if(!(r&&t.$refs.vsScreen&&t.$refs.vsScreen.waitForDataInit)){n.next=19;break}return n.next=19,t.$refs.vsScreen.waitForDataInit();case 19:n.next=25;break;case 21:n.prev=21,n.t0=n["catch"](2),console.error("获取演练详情失败:",n.t0),t.$message.error("获取演练详情失败，请重试");case 25:return n.prev=25,r&&(t.vsScreenLoading=!1),n.finish(25);case 28:case"end":return n.stop()}}),n,null,[[2,21,25,28]])})))()},handleMessage:function(e){var t=e.channel,n=e.data;if("DRILL_STAGE"===t){var r=n.drillTaskId,a=n.currentStageId,s=n.nextStageId,c=n.newCurrentStage;if(r!=this.drillTaskId)return console.log("drillTaskId不一致");this.currentStep=a,this.nextStep=s,this.newCurrentStage=c,this.queryDetailInfo(!1)}}}},u=l,S=(n("3adc"),n("2877")),f=Object(S["a"])(u,r,a,!1,null,"d3d9e2a8",null);t["default"]=f.exports}}]);