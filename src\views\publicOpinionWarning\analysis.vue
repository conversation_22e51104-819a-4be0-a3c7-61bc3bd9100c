<template>
    <div class="warning-analysis">
        <div class="analysis-filter">
            <el-form :inline="true" :model="queryParams" size="small">
                <el-form-item label="发送时间:" label-width="100px">
                    <el-date-picker v-model="queryParams.startTime" type="datetime" placeholder="开始日期" clearable
                        format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptionsStart" @change="startChange">
                    </el-date-picker>
                    -
                    <el-date-picker v-model="queryParams.endTime" type="datetime" placeholder="结束日期" clearable
                        format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptionsEnd" @change="endChange">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="所属方案" prop="planId" label-width="100px">
                    <el-select v-model="queryParams.planId" placeholder="请选择方案" clearable size="small">
                        <el-option v-for="item in caseData" :key="item.planId" :label="item.planName"
                            :value="item.planId" />
                    </el-select>
                </el-form-item>
            </el-form>
            <div class="search-btn" style="margin-bottom: 18px;">
                <el-button type="primary" size="mini" @click="queryData">查询</el-button>
            </div>
        </div>

        <div class="analysis-contain" id="myDom1">
            <div class="chart-wrap">
                <div class="chart-title">
                    <img src="@/assets/images/msgSource.png" alt="">
                    <div class="chart-name">與情源分布</div>
                    <el-tooltip placement="top" effect="light">
                        <div slot="content">
                            时间段内，微博、微信、网站等多个来源类型的信息占比情况。
                        </div>
                        <img src="@/assets/images/icon-question.png" alt="" class="name-question">
                    </el-tooltip>
                </div>
                <div class="chart-main">
                    <div class="chart-table">
                        <div class="chart-table-title">
                            <div class="chart-table-title-leftBlock"></div>
                            <div class="chart-table-title-text">舆情关注度分布</div>
                        </div>
                        <FocusAreaMap style="width: 100%;" ref="focusMapRef" :chartParams="queryParams"></FocusAreaMap>
                    </div>
                    <div class="chart-table">
                        <div class="chart-table-title">
                            <div class="chart-table-title-leftBlock"></div>
                            <div class="chart-table-title-text">热点事件分布</div>
                        </div>
                        <HotEventMap style="width: 100%;" ref="hotEventMapRef" :chartParams="queryParams"></HotEventMap>
                    </div>
                </div>
            </div>

            <div class="chart-wrap">
                <div class="chart-title">
                    <img src="@/assets/images/msgSource.png" alt="">
                    <div class="chart-name">與情源分布</div>
                    <el-tooltip placement="top" effect="light">
                        <div slot="content">
                            时间段内，微博、微信、网站等多个来源类型的信息占比情况。
                        </div>
                        <img src="@/assets/images/icon-question.png" alt="" class="name-question">
                    </el-tooltip>
                </div>
                <div class="chart-main">
                    <div class="chart-pie">
                        <pieChart v-if="true" style="width:100%;height:100%" :showLoading="sourceLoading"
                            :toolName="'與情源分布'" :data="sourceData" :radius="['30%', '50%']" :color="colorList"
                            @chartRef="chartToImg" :isToImg="'pie2'" :isDown="true" />
                    </div>
                    <div class="chart-table">
                        <div class="chart-table-title">
                            <div class="chart-table-title-leftBlock"></div>
                            <div class="chart-table-title-text">来源占比信息</div>
                        </div>
                        <el-table :data="sourceData.data" height="340" style="width: 100%" stripe
                            v-loading="sourceLoading">
                            <el-table-column prop="name" label="来源" align="center">
                            </el-table-column>
                            <el-table-column prop="value" label="信息量" align="center">
                            </el-table-column>
                            <el-table-column prop="percent" label="占比" align="center">
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>

            <div class="chart-wrap">
                <div class="chart-title">
                    <img src="@/assets/images/wordCloud.png" alt="">
                    <div class="chart-name">舆情主题分布</div>
                    <el-tooltip placement="top" effect="light">
                        <div slot="content">
                            利用自然语义分析法，对该事件中所提及的关键词进行分词聚合，呈现出被提及频次最多的关键词；<br>
                            字号越大的词组，被提及频次越多。
                        </div>
                        <img src="@/assets/images/icon-question.png" alt="" class="name-question">
                    </el-tooltip>
                </div>
                <div class="chart-main">
                    <div class="chart-pie">
                        <cloudChart v-if="true" v-show="cloudData.length" :showLoading="cloudLoading"
                            style="width:100%;height:100%" ref="cloud" :data="cloudData" @filterCloud="filterLists"
                            @chartRef="chartToImg" :isToImg="'cloud'" :isDown="true"></cloudChart>
                        <div v-if="cloudData.length ==0 && !cloudLoading" class="noneData">
                            <img src="@/assets/images/none.png" alt="">
                            <div>暂无数据</div>
                        </div>
                    </div>
                    <div class="chart-table">
                        <div class="chart-table-title">
                            <div class="chart-table-title-leftBlock"></div>
                            <div class="chart-table-title-text">热门词频</div>
                        </div>
                        <el-table :data="sensitiveTableData" height="340" style="width: 100%" v-loading="cloudLoading"
                            stripe>
                            <el-table-column type="index" label="排名" width="80" align="center">
                                <template slot-scope="scope">
                                    <div v-html="indexMethod(scope.$index + 1)"></div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="name" label="热词" align="center" show-overflow-tooltip>
                            </el-table-column>
                            <el-table-column prop="value" label="提及量" align="center">
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>

            <div class="overview" v-loading="sourceLoading">
                <div class="overview-list overview-total">
                    <div class="overview-content">
                        <p class="overview-name">{{"数据总量"}}</p>
                        <p class="overview-number" v-if="legendData[0]">{{legendData[0].value||0}}</p>
                        <p class="overview-number" v-else>0</p>
                    </div>
                </div>
                <div class="overview-list overview-item" v-for="(item,index) in legendData.slice(1)" :key="index">
                    <div class="overview-content-item">
                        <img class="overview-img" :src="transImage(item.type)" alt="无图片" />
                        <p class="overview-name">{{item.name=='总数'?"数据总量":item.name}}</p>
                        <p class="overview-number">{{item.value||0}}</p>
                    </div>
                </div>
            </div>

            <div class="chart-wrap">
                <div class="chart-title">
                    <img src="@/assets/images/msgSource.png" alt="">
                    <div class="chart-name">舆情走势图</div>
                    <el-tooltip placement="top" effect="light">
                        <div slot="content">
                            该时间段内分时段的多个来源类型的信息参与变化走势。
                        </div>
                        <img src="@/assets/images/icon-question.png" alt="" class="name-question">
                    </el-tooltip>
                </div>
                <div class="chart-main">
                    <lineChart v-if="true" style="width:100%;height:100%" :data="infoLineData" :legendData="legendData"
                        chartText="" :isShow="true" :toolName="'舆情走势'" :showLoading="infoLineLoading"
                        @chartRef="chartToImg" :isToImg="'line1'" :isDown="true" />
                </div>
            </div>

            <div class="chart-wrap">
                <div class="chart-title">
                    <img src="@/assets/images/sensitiveIcon.png" alt="">
                    <div class="chart-name">敏感占比图</div>
                    <el-tooltip placement="top" effect="light">
                        <div slot="content">
                            敏感判定由自建的情感研判模型完成。通过对内容精准切分词、中文语义分析、通过词距词序词频计算并按权重打分等方式，<br>
                            根据模型训练结果的判定标准，对内容进行情感判定。
                        </div>
                        <img src="@/assets/images/icon-question.png" alt="" class="name-question">
                    </el-tooltip>
                </div>
                <div class="chart-main">
                    <div class="chart-pie">
                        <pieChart v-if="true" style="width:100%;height:100%" :showLoading="emotionLoading"
                            :toolName="'敏感占比'" :data="emotionData" @chartRef="chartToImg" :isToImg="'pie1'"
                            :isDown="true" />
                    </div>
                    <div class="chart-table">
                        <div class="chart-table-title">
                            <div class="chart-table-title-leftBlock"></div>
                            <div class="chart-table-title-text">敏感信息TOP10</div>
                        </div>
                        <el-table :data="emotionTopData" height="340" v-loading="emotionTopLoading" style="width: 100%"
                            stripe>
                            <el-table-column type="index" label="排名" width="80" align="center">
                                <template slot-scope="scope">
                                    <div v-html="indexMethod(scope.$index + 1)"></div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <!-- <div class="mediClass" @click="goDetail(scope.row)">{{ scope.row.title}}</div> -->
                                    <div>{{ scope.row.title}}</div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="publishTime" label="发布时间" width="160" align="center">
                            </el-table-column>
                            <el-table-column prop="similarCount" label="相似文章" width="100" align="center">
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import moment from 'moment'
import { transImage } from '@/utils/index';
import lineChart from '@/views/fullSearch/components/lineChart.vue'
import pieChart from '@/views/fullSearch/components/pieChart.vue'
import cloudChart from '@/views/fullSearch/components/cloudChart.vue'
import FocusAreaMap from './components/FocusAreaMap.vue'
import HotEventMap from './components/HotEventMap.vue'

import { emotionAnalyse, mediaTypeAnalyse, emotionAnalyseTop, wordAnalyse, getType } from "@/api/publicOpinionWarning/index";
import { manageAllApi } from '@/api/publicOpinionWarning/index.js'
import { DeleteHotWord } from '@/api/home/<USER>'
export default {
    components: {
        lineChart,
        pieChart,
        cloudChart,
        FocusAreaMap,
        HotEventMap
    },
    data() {
        return {
            queryParams: {
                startTime: '',
                endTime: '',
                planId: ''
            },
            pickerOptionsStart: {
                disabledDate: (time) => {
                    let endDateVal = this.queryParams.endTime;
                    if (endDateVal) {
                        const endDate = new Date(endDateVal);
                        return time.getTime() > endDate.getTime() && time.toDateString() !== endDate.toDateString();
                    }
                },
            },
            pickerOptionsEnd: {
                disabledDate: (time) => {
                    let beginDateVal = this.queryParams.startTime;
                    if (beginDateVal) {
                        const beginDate = new Date(beginDateVal);
                        return time.getTime() < beginDate.getTime() && time.toDateString() !== beginDate.toDateString();
                    }
                },
            },

            caseData: [],

            copyTimeParams: {},
            imgEchart: {
                lineImg1: '',
                barMediaImg: '',
                lineImg2: '',
                pieImg1: '',
                pieImg2: '',
                cloudImg: '',
                barImg: '',
                mapImg: '',
                barAreaImg: '',
            },
            transImage,
            legendData: [
                { name: "总数", value: 0 },
                { name: "客户端", value: 0, percent: "0.00%", type: "6" },
                { name: "微博", value: 0, percent: "0.00%", type: "3" },
                { name: "微信", value: 0, percent: "0.00%", type: "5" },
                { name: "短视频", value: 0, percent: "0.00%", type: "11" },
                { name: "新闻", value: 0, percent: "0.00%", type: "1" },
                { name: "论坛社区", value: 0, percent: "0.00%", type: "0" },
                { name: "政务", value: 0, percent: "0.00%", type: "25" },
                { name: "评论", value: 0, percent: "0.00%", type: "26" },
                { name: "电子报刊", value: 0, percent: "0.00%", type: "17" }
            ],
            infoLineLoading: false,


            infoLineData: {},

            sourceLoading: false,
            sourceData: {},
            emotionData: {},
            emotionLoading: false,
            sensitiveTableData: [],
            colorList: ['#518DEB', '#FF7800', '#E770B0', '#08C47A', '#664AE7', '#EC6764', '#98C20A'],
            cloudData: [],
            cloudLoading: false,
            emotionTopData: [],
            emotionTopLoading: false,

            emotionParams: { negative: 0, neutral: 0, positive: 0, total: 0 },
            peakValue: null,
            newParams: {},
            mapData: [],
            mediaLists: [],

        }
    },
    props: {
        type: {
            default: 'second',
            type: String
        },
        isFilter: {
            type: Boolean,
            default: false
        },
    },
    created() {
        this.getDict()
        this.queryParams.planId = this.$route.query.planId
        this.queryParams.startTime = this.$route.query.warnDateStart
        this.queryParams.endTime = this.$route.query.warnDateEnd
    },
    mounted() {
        this.queryData()
    },

    methods: {
        async getDict() {
            const res = await this.getDicts('sys_media_type')
            this.mediaLists = res.data

            const caseRes = await manageAllApi()
            this.caseData = caseRes.data
        },

        startChange(val) {
            let endDateVal = this.queryParams.endTime;
            if (endDateVal && new Date(this.queryParams.startTime).getTime() > new Date(endDateVal).getTime()) {
                this.queryParams.endTime = val;
            }
        },
        endChange(val) {
            let beginDateVal = this.queryParams.startTime;
            if (beginDateVal && new Date(this.queryParams.endTime).getTime() < new Date(beginDateVal).getTime()) {
                this.queryParams.startTime = val;
            }
        },



        chartToImg(val, base) {
            // 确保 imgEchart 已被初始化
            if (!this.imgEchart) {
                this.imgEchart = {};
            }
            const imgMappings = {
                'line1': 'lineImg1',
                'barMedia': 'barMediaImg',
                'line2': 'lineImg2',
                'pie1': 'pieImg1',
                'pie2': 'pieImg2',
                'cloud': 'cloudImg',
                'bar': 'barImg',
                'map': 'mapImg',
                'barArea': 'barAreaImg',
                'bar2': 'barImg2',
                'tree': 'spreadPathImg',
            };
            //   const
            const propName = imgMappings[val]
            this.imgEchart[propName] = base;
        },

        // 跳转详情页
        async goDetail(row) {
            console.log('row :>> ', row);
            const fullPath = this.$router.resolve({
                path: '/fullSearch/dataDetail',
                query: { id: row.id, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5 }
            })
            window.open(fullPath.href, '_blank')
        },
        async queryData() {
            if (!this.queryParams.startTime || !this.queryParams.endTime) {
                this.$message.error('请选择正确的时间段')
                return
            }
            // this.copyTimeParams = JSON.parse(JSON.stringify(this.queryParams))
            this.queryEmotionAnalyse(this.copyTimeParams)
            this.queryEmotionTop(this.copyTimeParams)
            this.getCloudData(this.copyTimeParams)

            this.queryMediaAnalyse(this.copyTimeParams)
            this.getinfoLineData(this.copyTimeParams)

            // 触发地图组件数据更新
            this.refreshMapData()
        },

        // 刷新地图数据
        refreshMapData() {
            // 通过 $refs 调用子组件的刷新方法
            if (this.$refs.focusMapRef) {
                this.$refs.focusMapRef.refreshData()
            }
            if (this.$refs.hotEventMapRef) {
                this.$refs.hotEventMapRef.refreshData()
            }
        },

        // 过滤信息
        filterLists(item) {
            if (this.isFilter) {
                let filterParams = { word: item.name, planId: this.queryParams.planId }
                this.$confirm('此操作将过滤热词, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    DeleteHotWord(filterParams).then(res => {
                        if (res.code == 200) {
                            this.$message.success('操作成功')
                            this.getCloudData(this.copyTimeParams)
                        }
                    })
                })
            }
        },



        //舆情走势图
        async getinfoLineData(copyTimeParams) {
            try {
                this.infoLineLoading = true
                let res = await getType({ ...this.queryParams, ...copyTimeParams })//替换
                this.infoLineData = res.data
                const num = res.data.seriesList[0].data
                this.peakValue = Math.max(...num);
            } finally {
                this.infoLineLoading = false
            }
        },

        // 信息来源占比
        async queryMediaAnalyse(copyTimeParams) {
            try {
                this.sourceLoading = true
                let res = await mediaTypeAnalyse({ ...this.queryParams, ...copyTimeParams })
                this.sourceData = { data: res.data }
                let legendData = JSON.parse(JSON.stringify(res.data))
                const sum = legendData.reduce((accumulator, current) => accumulator + current.value, 0);
                this.legendData = [{ name: '总数', value: sum }, ...legendData]
            } finally {
                this.sourceLoading = false
            }
        },
        // 敏感信息占比
        async queryEmotionAnalyse(copyTimeParams) {
            try {
                this.emotionLoading = true
                let res = await emotionAnalyse({ ...this.queryParams, ...copyTimeParams })
                this.emotionData = { data: res.data.data }
                this.emotionParams = res.data.prarms
            } finally {
                this.emotionLoading = false
            }
        },
        // 敏感信息top10
        async queryEmotionTop(copyTimeParams) {
            try {
                this.emotionTopLoading = true
                let res = await emotionAnalyseTop({ ...this.queryParams, ...copyTimeParams })
                this.emotionTopData = res.data
            } finally {
                this.emotionTopLoading = false
            }
        },
        indexMethod(index) {
            if (index == 1) {
                return `<div style="color:#FF0000">${index}</div>`
            } else if (index == 2) {
                return `<div style="color:#FF7F2D">${index}</div>`
            } else if (index == 3) {
                return `<div style="color:#FFB10E">${index}</div>`
            } else {
                return index
            }
        },
        // 热词云
        async getCloudData(copyTimeParams) {
            this.cloudData = []
            this.cloudLoading = true
            wordAnalyse({ ...this.queryParams, ...copyTimeParams }).then(res => {
                this.cloudData = res.data || []
                let params = JSON.parse(JSON.stringify(res.data))
                this.sensitiveTableData = params.length <= 10 ? [] : params.slice(0, 10)
                this.cloudLoading = false
            }).catch(err => {
                this.cloudLoading = false
            })
        },


    }
}
</script>
<style scoped lang="scss">
.warning-analysis {
    background: #F2F1F3;
    padding: 20px;

    .analysis-filter {
        padding-top: 20px;
        display: flex;
        align-items: center;
        background-color: #FFFFFF;
    }

    .analysis-contain {
        margin-top: 20px;
        padding: 20px;
        background-color: #FFFFFF;

        .chart-wrap {
            margin-bottom: 50px;

            .chart-title {
                display: flex;
                align-items: center;
                padding-bottom: 10px;
                border-bottom: 1px solid #EEE;
                position: relative;

                .chart-name {
                    margin: 0 10px;
                    font-weight: 500;
                    font-size: 18px;
                    color: #333333;
                    line-height: 25px;
                }

                img {
                    width: 18px;

                    &.name-question {
                        width: 16px;
                    }
                }
            }

            .chart-main {
                padding-top: 25px;
                height: 400px;
                display: flex;
                justify-content: space-around;

                .noneData {

                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: unset;
                        height: 80px;
                        margin-bottom: 10px;
                    }
                }


            }

            .chart-pie {
                width: 50%;

                .noneData {
                    margin-top: 20%;
                }
            }

            .chart-table {
                width: 50%;

                .noneData {

                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    margin-top: 30%;

                    img {
                        width: unset;
                        height: 80px;
                        margin-bottom: 10px;
                    }
                }

                .chart-media-wrap {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .chart-table-title {
                    padding: 10px 0;

                    .chart-table-title-leftBlock {
                        width: 4px;
                        height: 16px;
                        background: #247CFF;
                        display: inline-block;
                        vertical-align: middle;
                        margin-right: 2px;
                    }

                    .chart-table-title-text {
                        font-size: 14px;
                        color: #333333;
                        display: inline-block;
                        vertical-align: middle;
                        font-family: PingFangSC, PingFang SC;
                    }
                }

                ::v-deep.el-table {
                    border: 1px solid #EFEFEF;
                    border-bottom: #EFEFEF;
                    border-right: 1px solid #EFEFEF;

                    .el-table--striped .el-table__body tr.el-table__row--striped.el-table__row--striped .el-table__row--striped td {
                        background-color: #f9f9fc;
                    }
                }
            }

        }

        .overview {
            display: flex;
            align-items: stretch;
            /* 让子元素在高度上拉伸 */
            justify-content: space-between;
            margin-bottom: 50px;

            .overview-list {
                // width: 10%;
                flex: 1;
                box-sizing: border-box;
                position: relative;
                color: #fff;
                font-size: 1.57vw;
                line-height: 1.87vw;
                margin-left: 1vw;
                height: auto;
            }

            .overview-list:nth-child(1) {
                flex: 1.5;
                aspect-ratio: 40/24;
                margin-left: 0;
                height: 100%;
                max-width: 15%;
            }

            .overview-item {
                color: #000;
                background: #FFFFFF;
                // box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.15);
                box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
                border-radius: 8px;
                background-color: #eaf6ff;
            }

            .overview-total {
                background: url("../../assets/images/total.png") no-repeat center / cover;
                background-size: 100%;
            }

            .overview-content {
                // padding: 1.8vw 0 0 50%;
                padding: 0 0 0 40%;
                display: flex;
                flex-direction: column;
                height: 100%;
                justify-content: center;

                p {
                    margin: 0;
                }

                .overview-name {
                    // margin-bottom: 0.36vw;
                    font-size: 0.83vw;
                    line-height: 1.2vw;
                }

                .overview-number {
                    word-wrap: break-word;
                    font-size: 1.3vw;
                    line-height: normal;
                }
            }

            .overview-content-item {
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                p {
                    margin: 0;
                }

                .overview-name {
                    font-size: 0.83vw;
                    line-height: 1.2vw;
                    color: #666666;
                }

                .overview-img {
                    width: 1.3vw;
                }
            }
        }

    }

    .mediClass {
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;

        &:hover {
            color: #247CFF;
        }
    }
}
</style>
