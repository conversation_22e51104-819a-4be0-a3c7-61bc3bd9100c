(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-07dcd4fa"],{"265d":function(t,e,a){"use strict";a.d(e,"n",(function(){return i})),a.d(e,"p",(function(){return l})),a.d(e,"q",(function(){return n})),a.d(e,"o",(function(){return s})),a.d(e,"h",(function(){return o})),a.d(e,"m",(function(){return u})),a.d(e,"d",(function(){return d})),a.d(e,"s",(function(){return c})),a.d(e,"g",(function(){return m})),a.d(e,"f",(function(){return p})),a.d(e,"e",(function(){return b})),a.d(e,"r",(function(){return f})),a.d(e,"l",(function(){return k})),a.d(e,"k",(function(){return h})),a.d(e,"a",(function(){return g})),a.d(e,"b",(function(){return A})),a.d(e,"c",(function(){return v})),a.d(e,"i",(function(){return C})),a.d(e,"j",(function(){return O}));var r=a("b775");function i(t){return Object(r["a"])({url:"/drill/task/query",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/drill/task/save",method:"post",data:t})}function n(t){return Object(r["a"])({url:"/drill/task/user",method:"post",data:t})}function s(t){return Object(r["a"])({url:"/drill/task/query/one",method:"post",data:t})}function o(t){return Object(r["a"])({url:"/drill/process/start",method:"post",data:t})}function u(t){return Object(r["a"])({url:"/drill/stage/next",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/drill/comment/publish",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/file/getUrlByIds",method:"post",data:t})}function m(t){return Object(r["a"])({url:"/drill/process/leave",method:"post",data:t})}function p(t){return Object(r["a"])({url:"/drill/process/end",method:"post",data:t})}function b(t){return Object(r["a"])({url:"/drill/comment/query",method:"post",data:t})}function f(t){return Object(r["a"])({url:"/drill/timer/start",method:"post",data:t})}function k(t){return Object(r["a"])({url:"/drill/sign/save",method:"post",data:t})}function h(t){return Object(r["a"])({url:"/drill/sign/query",method:"post",data:t})}function g(t){return Object(r["a"])({url:"/comment/like/add",method:"post",data:t})}function A(t){return Object(r["a"])({url:"/comment/like/cancel",method:"post",data:t})}function v(t){return Object(r["a"])({url:"/comment/reply/add",method:"post",data:t})}function C(t){return Object(r["a"])({url:"/drill/score/query",method:"post",data:t})}function O(t){return Object(r["a"])({url:"/drill/score/statistics",method:"post",data:t})}},"776f":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAUCAYAAADPym6aAAAAAXNSR0IArs4c6QAAAX5JREFUWEfV10FOwkAUBuD/NUwNrnWLG0k4gAs7xA2tC3fCJQQvYb2E4CGUA2gbozL1CBpWutW1RAb7zMSYgLR0yXS202T+r69N/xIK1sbhUz1N02cATtG1a9tnjKjocOGrAYCTouvWuU9AezXk6H5bTCuvAKrrDFpw9lgfeI2VEDcYhcx0ZjHCROvqSF7mQzxVFZv8BtCWtRDGu55gB4mc5EJEkPTAfGEtAgAzhbPYOzcZsyEhO+IxeQFj12LIRH+7NdztfeRC3CDpMPOVxQgzjr6Om6d/GTMnInylAHgWQ1LHcRpfN/vjXEilpSQRRhYjwMBwFsnOfMaliVR8NSTg2G4Iy1nUTHIhpagjgNKRbP6/0QsTEYHqg9G1eRrE6ExjOcyHlKiOIKQ0F1KKOsLU07FnSuzS+n20ylBHwB/6k2qmjuRCRCvpgrhv87sxX0eyIaaOPCTmx6luMWShjmRC3JZqM+HaYoRphAN9K3urMpLwlfmKS4shS3UkK+sPc2ySxtZ6AcEAAAAASUVORK5CYII="},b406:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"addSimulate"},[r("div",{staticClass:"pageTitle"},[r("div",{staticClass:"pageTitle_colorBlock"}),r("div",{staticClass:"pageTitle_title"},[t._v(t._s(t.pageTitle))])]),r("div",{staticClass:"dashedLine"}),r("div",{staticClass:"main-content"},[r("el-form",{ref:"form",attrs:{model:t.taskForm,"label-width":"160px",rules:t.rules}},[r("el-form-item",{attrs:{label:"任务标题：",prop:"taskTitle"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{maxlength:"100","show-word-limit":"",placeholder:"请输入标题"},model:{value:t.taskForm.taskTitle,callback:function(e){t.$set(t.taskForm,"taskTitle","string"===typeof e?e.trim():e)},expression:"taskForm.taskTitle"}})],1),r("el-form-item",{attrs:{label:"任务内容：",prop:"taskContent"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",maxlength:"1000","show-word-limit":"",autosize:{minRows:4,maxRows:6},placeholder:"请输入任务内容，不超过1000个字符"},model:{value:t.taskForm.taskContent,callback:function(e){t.$set(t.taskForm,"taskContent","string"===typeof e?e.trim():e)},expression:"taskForm.taskContent"}})],1),r("el-form-item",{attrs:{label:"演练事件：",prop:"drillEvent"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{maxlength:"100","show-word-limit":"",placeholder:"请输入事件"},model:{value:t.taskForm.drillEvent,callback:function(e){t.$set(t.taskForm,"drillEvent","string"===typeof e?e.trim():e)},expression:"taskForm.drillEvent"}})],1),r("el-form-item",{attrs:{label:"演练时间：",prop:"estimateDrillTime"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","picker-options":t.pickOptions,"value-format":"yyyy-MM-dd",placeholder:"请选择演练时间"},model:{value:t.taskForm.estimateDrillTime,callback:function(e){t.$set(t.taskForm,"estimateDrillTime",e)},expression:"taskForm.estimateDrillTime"}})],1),r("div",{staticClass:"group-top"},[r("img",{attrs:{src:a("776f"),alt:""}}),r("div",[t._v("蓝方")])]),r("el-form-item",{attrs:{label:"队长：",prop:"blueCaptain"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择队长"},model:{value:t.taskForm.blueCaptain,callback:function(e){t.$set(t.taskForm,"blueCaptain",e)},expression:"taskForm.blueCaptain"}},t._l(t.blueCaptainOptions,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value,disabled:t.disabled}})})),1)],1),r("el-form-item",{attrs:{label:"队员：",prop:"blueMember"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择队员"},model:{value:t.taskForm.blueMember,callback:function(e){t.$set(t.taskForm,"blueMember",e)},expression:"taskForm.blueMember"}},t._l(t.blueMemberOptions,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value,disabled:t.disabled}})})),1)],1),r("div",{staticClass:"group-top"},[r("img",{attrs:{src:a("f83d"),alt:""}}),r("div",[t._v("红方")])]),r("el-form-item",{attrs:{label:"队长：",prop:"redCaptain"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择队长"},model:{value:t.taskForm.redCaptain,callback:function(e){t.$set(t.taskForm,"redCaptain",e)},expression:"taskForm.redCaptain"}},t._l(t.redCaptainOptions,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value,disabled:t.disabled}})})),1)],1),r("el-form-item",{attrs:{label:"队员：",prop:"redMember"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择队员"},model:{value:t.taskForm.redMember,callback:function(e){t.$set(t.taskForm,"redMember",e)},expression:"taskForm.redMember"}},t._l(t.redMemberOptions,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value,disabled:t.disabled}})})),1)],1)],1),r("div",{staticClass:"btn-group"},["新增任务演练"==t.pageTitle||1==t.taskForm.status?r("el-button",{attrs:{type:"primary"},on:{click:t.onSubmit}},[t._v("确定")]):t._e(),r("el-button",{on:{click:t.onCancel}},[t._v("取消")])],1)],1)])},i=[],l=a("2909"),n=a("5530"),s=(a("99af"),a("caad"),a("a15b"),a("d81d"),a("d3b7"),a("2532"),a("0643"),a("a573"),a("9a9a"),a("265d")),o={data:function(){return{taskForm:{taskTitle:"",taskContent:"",drillEvent:"",estimateDrillTime:"",blueCaptain:"",blueMember:[],redCaptain:"",redMember:[]},rules:{taskTitle:[{required:!0,message:"请输入任务标题",trigger:"blur"}],taskContent:[{required:!0,message:"请输入任务内容",trigger:"blur"},{max:1e3,message:"任务内容请控制在1000个字以内",trigger:"change"}],drillEvent:[{required:!0,message:"请输入演练事件",trigger:"blur"}],estimateDrillTime:[{required:!0,message:"请输入演练时间",trigger:"blur"}],blueCaptain:[{required:!0,message:"请选择蓝方队长",trigger:"blur"}],blueMember:[{required:!0,message:"请选择蓝方队员",trigger:"blur"}],redCaptain:[{required:!0,message:"请选择红方队长",trigger:"blur"}],redMember:[{required:!0,message:"请选择红方队员",trigger:"blur"}]},pickOptions:{disabledDate:function(t){return t.getTime()<Date.now()-864e5}},peopleList:[],pageTitle:"新增任务演练"}},computed:{blueCaptainOptions:function(){var t=this;return this.peopleList.map((function(e){return Object(n["a"])(Object(n["a"])({},e),{},{disabled:[].concat(Object(l["a"])(t.taskForm.blueMember),[t.taskForm.redCaptain],Object(l["a"])(t.taskForm.redMember)).includes(e.value)})}))},blueMemberOptions:function(){var t=this;return this.peopleList.map((function(e){return Object(n["a"])(Object(n["a"])({},e),{},{disabled:[t.taskForm.blueCaptain,t.taskForm.redCaptain].concat(Object(l["a"])(t.taskForm.redMember)).includes(e.value)})}))},redCaptainOptions:function(){var t=this;return this.peopleList.map((function(e){return Object(n["a"])(Object(n["a"])({},e),{},{disabled:[].concat(Object(l["a"])(t.taskForm.redMember),[t.taskForm.blueCaptain],Object(l["a"])(t.taskForm.blueMember)).includes(e.value)})}))},redMemberOptions:function(){var t=this;return this.peopleList.map((function(e){return Object(n["a"])(Object(n["a"])({},e),{},{disabled:[t.taskForm.redCaptain,t.taskForm.blueCaptain].concat(Object(l["a"])(t.taskForm.blueMember)).includes(e.value)})}))}},mounted:function(){var t,e=this;if(this.getDist(),null!==(t=this.$route.query)&&void 0!==t&&t.drillTaskId){this.pageTitle="编辑任务演练",this.resetForm();var a={drillTaskId:this.$route.query.drillTaskId};Object(s["o"])(a).then((function(t){"200"==t.code&&(e.taskForm=t.data)}))}else this.pageTitle="新增任务演练",this.resetForm()},methods:{getDist:function(){var t=this;Object(s["q"])().then((function(e){"200"==e.code&&(t.peopleList=e.data.map((function(t){return{value:t.userId,label:t.nickName}})))}))},validateTeams:function(){var t=this.taskForm,e=t.blueCaptain,a=t.blueMember,r=void 0===a?[]:a,i=t.redCaptain,l=t.redMember,n=void 0===l?[]:l,s=[];return e&&e===i&&s.push("红蓝队长不能为同一人"),r.includes(e)&&s.push("蓝队队长不能兼任队员"),n.includes(i)&&s.push("红队队长不能兼任队员"),r.some((function(t){return n.includes(t)}))&&s.push("存在重复的双方队员"),e&&n.includes(e)&&s.push("蓝队队长不能担任红方队员"),i&&r.includes(i)&&s.push("红队队长不能担任蓝方队员"),s},onSubmit:function(){var t=this;this.$refs.form.validate((function(e){if(e){var a=t.validateTeams();if(a.length>0)return void t.$message.error(a.join("，"));var r=Object(n["a"])({},t.taskForm);console.log("params",r),Object(s["p"])(r).then((function(e){t.$message.success("保存成功"),t.onCancel()})).catch((function(e){t.$message.error("保存失败")}))}}))},onCancel:function(){this.$router.push({path:"/simulatedVS/index"})},resetForm:function(){this.$refs.form.resetFields(),this.taskForm={taskTitle:"",taskContent:"",drillEvent:"",estimateDrillTime:"",blueCaptain:"",blueMember:[],redCaptain:"",redMember:[]}}}},u=o,d=(a("dad7"),a("2877")),c=Object(d["a"])(u,r,i,!1,null,"8e219802",null);e["default"]=c.exports},bf23:function(t,e,a){},dad7:function(t,e,a){"use strict";a("bf23")},f83d:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAUCAYAAADPym6aAAAAAXNSR0IArs4c6QAAAYNJREFUWEfV1kFOwkAUBuD/dWoIG1e0cWFwI5FYcW+9hHAJwUuIlxA8hHIKcOdmwGBYaYwL2o1uiAnTZwqaIHTaJdOumnSS/l/fTPoTMq5nB5UIYgzAylq7xed9ynq5dEWXGJdZ67b53CLUUyFPe3AKSrwyUNxm0PR308QL5tVUiHTtNjFfm4sAiLnphdGdFjLYR3H3W7wBKJkKISD4LKgD/x0zLUS6VouYbk1FxLmIue2F0c3iPikoA9bIES8ADk2FEDCzd1T56AOhFiJd0SDGvamI5TTQ8UJ19ZcxcSJDRwwAnBkMiSyo6nGAiRYiXdsn5r7BiPg89LxANVYzbkxElkSPCBcmQyIi/3Q6f9RCclJHBieBOl//0P8mMiqJDhOaJk+DCY3aVPW0kJzVkUgLyUMdYeJWbRp1k3bMYmvloY4ACL8KqhzXES1EulaTmDomn43VOpIIWdYRewxwxVTIeh1JhEhX1InxYCrit450vVC10jLS0BHxX9w3GLJRR5Ky/gDlVIsFam21jgAAAABJRU5ErkJggg=="}}]);