(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6f3a5038"],{"07ac":function(t,e,a){var s=a("23e7"),n=a("6f53").values;s({target:"Object",stat:!0},{values:function(t){return n(t)}})},"15fc":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"entity-identification"},[a("el-card",{staticClass:"container",attrs:{shadow:"never"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t._v("实体识别")]),a("div",{staticClass:"panel-body"},[a("div",{staticClass:"text-content",domProps:{innerHTML:t._s(t.originalText)}}),a("br"),a("div",{staticClass:"keywords-container"},[a("span",{staticClass:"text-main"},[t._v("关键词提取：")]),a("span",t._l(t.keywordsList,(function(e){return a("span",[t._v(" "+t._s(e))])})),0)])])]),a("el-card",{staticClass:"container",attrs:{shadow:"never"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t._v("分类统计")]),a("div",{staticClass:"panel-body"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{ref:"map1",staticClass:"chart-container"})]),a("el-col",{attrs:{span:8}},[a("div",{ref:"map2",staticClass:"chart-container"})]),a("el-col",{attrs:{span:8}},[a("div",{ref:"map3",staticClass:"chart-container"})])],1)],1)]),a("el-card",{staticClass:"container",attrs:{shadow:"never"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t._v("关联关系挖掘")]),a("div",{staticClass:"panel-body"},[a("div",{ref:"map4",staticClass:"relation-chart"}),a("div",{staticClass:"sentence-container"},[a("span",{staticClass:"text-main"},[t._v("摘要提取：")]),a("span",[t._v(t._s(t.sentenceText))])])])])],1)},n=[],i=(a("b64b"),a("d3b7"),a("07ac"),a("0643"),a("4e3e"),a("159b"),a("313e")),r=a("986b"),o={name:"EntityIdentification",data:function(){return{originalText:"",keywordsList:"",sentenceText:"",id:this.$route.query.id||"",time:this.$route.query.time||"",loading:!1,charts:{map1:null,map2:null,map3:null,map4:null}}},mounted:function(){var t=this;this.$nextTick((function(){t.initCharts(),t.getEntityOption()}))},beforeDestroy:function(){this.disposeCharts(),window.removeEventListener("resize",this.handleResize)},methods:{initCharts:function(){var t=this,e=["map1","map2","map3","map4"],a={map4:{renderer:"canvas"}};e.forEach((function(e){if(t.$refs[e]){var s=a[e]||{};t.charts[e]=i["init"](t.$refs[e],null,s)}else console.warn("图表容器 ".concat(e," 不存在"))})),window.addEventListener("resize",this.handleResize)},disposeCharts:function(){var t=this;Object.values(this.charts).forEach((function(t){t&&t.dispose()})),Object.keys(this.charts).forEach((function(e){t.charts[e]=null}))},handleResize:function(){Object.values(this.charts).forEach((function(t){t&&t.resize()}))},getEntityOption:function(){var t=this;this.loading=!0;var e={id:this.id,time:this.time};Object(r["k"])(e).then((function(e){var a=e.data;t.originalText=a.text,t.keywordsList=a.keywordList,t.sentenceText=a.sentence,t.renderCharts(a),t.loading=!1})).catch((function(e){console.error("获取数据失败:",e),t.loading=!1,t.$message.error("获取数据失败")}))},renderCharts:function(t){this.renderChart("map1",this.getMap(t.nameJson,"人名","#ff7f50")),this.renderChart("map2",this.getMap(t.placeJson,"地名","#87cefa")),this.renderChart("map3",this.getMap(t.organizationJson,"机构名","#da70d6")),this.renderChart("map4",this.getLinkOption(t.relevance))},renderChart:function(t,e){var a=this.charts[t];a?(a.showLoading({text:"数据正在加载..."}),this.$nextTick((function(){a.setOption(e,!0),a.hideLoading()}))):console.warn("图表 ".concat(t," 未初始化"))},getMap:function(t,e,a){var s=[],n=[];for(var i in t)s.push(i),n.push(t[i]);return 0===s.length&&(s=["暂无数据"],n=[0]),{title:{textStyle:{color:"#98a2b0"},text:e},textStyle:{color:"#98a2b0"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},toolbox:{show:!0,feature:{saveAsImage:{show:!0},restore:{show:!0},dataView:{show:!0,readOnly:!1},magicType:{show:!0,type:["line","bar"]}}},xAxis:{type:"value",boundaryGap:[0,.01],splitLine:{lineStyle:{color:["#eee"]}}},yAxis:{type:"category",data:s},series:[{name:"数量",type:"bar",barWidth:"15px",data:n,itemStyle:{normal:{color:a}}}]}},getLinkOption:function(t){var e=t;return{tooltip:{trigger:"item",formatter:"{a} : {b}"},toolbox:{show:!0,feature:{restore:{show:!0},saveAsImage:{show:!0}}},legend:[{textStyle:{color:"#98a2b0"},data:["实体类","实体"]}],animation:!1,color:["#FF7F50","#87CEFA"],series:[{name:"关联关系",type:"graph",layout:"force",data:e.nodes,links:e.links,categories:[{name:"文章"},{name:"实体类"},{name:"实体"}],roam:!0,symbolSize:25,itemStyle:{normal:{label:{show:!0,textStyle:{color:"#333"}}}},force:{repulsion:100}}]}}}},c=o,l=(a("5440"),a("2877")),d=Object(l["a"])(c,s,n,!1,null,"38518f7a",null);e["default"]=d.exports},5440:function(t,e,a){"use strict";a("a21f")},"6f53":function(t,e,a){var s=a("83ab"),n=a("e330"),i=a("df75"),r=a("fc6a"),o=a("d1e7").f,c=n(o),l=n([].push),d=function(t){return function(e){var a,n=r(e),o=i(n),d=o.length,h=0,u=[];while(d>h)a=o[h++],s&&!c(n,a)||l(u,t?[a,n[a]]:n[a]);return u}};t.exports={entries:d(!0),values:d(!1)}},a21f:function(t,e,a){}}]);