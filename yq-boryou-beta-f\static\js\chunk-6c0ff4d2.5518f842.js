(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6c0ff4d2"],{"04d1":function(t,e,n){var r=n("342f"),i=r.match(/firefox\/(\d+)/i);t.exports=!!i&&+i[1]},"0ccb":function(t,e,n){var r=n("e330"),i=n("50c4"),a=n("577e"),o=n("1148"),s=n("1d80"),u=r(o),h=r("".slice),d=Math.ceil,l=function(t){return function(e,n,r){var o,l,c=a(s(e)),f=i(n),m=c.length,g=void 0===r?" ":a(r);return f<=m||""==g?c:(o=f-m,l=u(g,d(o/g.length)),l.length>o&&(l=h(l,0,o)),t?c+l:l+c)}};t.exports={start:l(!1),end:l(!0)}},"13d5":function(t,e,n){"use strict";var r=n("23e7"),i=n("d58f").left,a=n("a640"),o=n("2d00"),s=n("605d"),u=a("reduce"),h=!s&&o>79&&o<83;r({target:"Array",proto:!0,forced:!u||h},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},"48c3":function(t,e,n){(function(e,r){t.exports=r(n("2b0e"))})("undefined"!==typeof self&&self,(function(t){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="01e5")}({"01e5":function(t,e,n){"use strict";if(n.r(e),"undefined"!==typeof window){var r=window.document.currentScript,i=r&&r.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);i&&(n.p=i[1])}var a=function(){var t=this,e=t._self._c;return e("div",{ref:"container",staticClass:"vue-danmaku"},[e("div",{ref:"dmContainer",class:["danmus",{show:!t.hidden},{paused:t.paused}]}),t._t("default")],2)},o=[],s=n("8bbf"),u=n.n(s),h={model:{prop:"danmus",event:"input"},props:{danmus:{type:Array,required:!0,default:()=>[]},channels:{type:Number,default:0},autoplay:{type:Boolean,default:!0},loop:{type:Boolean,default:!1},useSlot:{type:Boolean,default:!1},debounce:{type:Number,default:100},speeds:{type:Number,default:200},randomChannel:{type:Boolean,default:!1},fontSize:{type:Number,default:18},top:{type:Number,default:4},right:{type:Number,default:0},isSuspend:{type:Boolean,default:!1},extraStyle:{type:String,default:""}},data(){return{$container:null,$dmContainer:null,container:{width:0,height:0},calcChannels:0,danmuHeight:0,danmuList:[],timer:null,index:0,hidden:!1,paused:!1,danChannel:{}}},computed:{danmu(){return{height:this.danmuHeight,fontSize:this.fontSize,speeds:this.speeds,top:this.top,right:this.right}},danmaku(){return{channels:this.channels||this.calcChannels,autoplay:this.autoplay,loop:this.loop,useSlot:this.useSlot,debounce:this.debounce,randomChannel:this.randomChannel}},dataWatcher(){return JSON.parse(JSON.stringify(this.danmuList))}},watch:{danmus:{handler(t){this.danmuList=[...t]},deep:!0,immediate:!0},dataWatcher:{handler(t,e){JSON.stringify(t)!==JSON.stringify(e)&&(this.$emit("input",t),this.$emit("change",t))},deep:!0}},created(){},mounted(){this.$nextTick(()=>{this.init()})},beforeDestroy(){this.clear()},methods:{init(){this.initCore(),this.initDanmuList(),this.isSuspend&&this.initSuspendEvents(),this.danmaku.autoplay&&this.play()},initCore(){this.$container=this.$refs.container,this.$dmContainer=this.$refs.dmContainer,this.container={width:this.$container.offsetWidth,height:this.$container.offsetHeight}},initDanmuList(){this.danmuList=[...this.danmus]},play(){this.paused=!1,this.timer||(this.timer=setInterval(()=>this.draw(),this.danmaku.debounce))},draw(){if(!this.paused&&this.danmuList.length)if(this.index>this.danmuList.length-1){const t=this.$refs.dmContainer.children.length;this.danmaku.loop&&t<this.index&&(this.$emit("list-end"),this.index=0,this.insert())}else this.insert()},insert(t){const e=this.danmaku.loop?this.index%this.danmuList.length:this.index,n=t||this.danmuList[e];let r=document.createElement("div");this.danmaku.useSlot?r=this.getSlotComponent(n,e).$el:(r.innerHTML=n,r.setAttribute("style",this.extraStyle),r.style.fontSize=this.danmu.fontSize+"px",r.style.lineHeight=this.danmu.fontSize+"px"),r.classList.add("dm"),this.$dmContainer.appendChild(r),r.style.opacity=0,this.$nextTick(()=>{this.danmu.height||(this.danmuHeight=r.offsetHeight),this.channels||(this.calcChannels=Math.floor(this.container.height/(this.danmu.height+this.danmu.top)));let t=this.getChannelIndex(r);if(t>=0){const n=r.offsetWidth,i=this.danmu.height;r.classList.add("move"),r.dataset.index=e,r.style.opacity=1,r.style.top=t*(i+this.danmu.top)+"px",r.style.width=n+this.danmu.right+"px",r.style.setProperty("--dm-scroll-width",`-${this.container.width+n}px`),r.style.left=this.container.width+"px",r.style.animationDuration=this.container.width/this.danmu.speeds+"s",r.addEventListener("animationend",()=>{+r.dataset.index!==this.danmuList.length-1||this.danmaku.loop||this.$emit("play-end",r.dataset.index),this.$dmContainer&&this.$dmContainer.removeChild(r)}),this.index++}else this.$dmContainer&&this.$dmContainer.removeChild(r)})},getSlotComponent(t,e){const n=this,r=u.a.extend({props:{danmu:[String,Object],index:Number},render(t){return t("div",[n.$scopedSlots.dm({danmu:this.danmu,index:this.index})])}}),i=new r({propsData:{danmu:t,index:e}}).$mount(document.createElement("div"));return i},getChannelIndex(t){let e=[...Array(this.danmaku.channels).keys()];this.danmaku.randomChannel&&(e=e.sort(()=>.5-Math.random()));for(let n of e){const e=this.danChannel[n];if(!e||!e.length)return this.danChannel[n]=[t],t.addEventListener("animationend",()=>this.danChannel[n].splice(0,1)),n%this.danmaku.channels;for(let r=0;r<e.length;r++){const i=this.getDanRight(e[r])-10;if(i<=.88*(t.offsetWidth-e[r].offsetWidth)||i<=0)break;if(r===e.length-1)return this.danChannel[n].push(t),t.addEventListener("animationend",()=>this.danChannel[n].splice(0,1)),n%this.danmaku.channels}}return-1},getDanRight(t){const e=t.offsetWidth||parseInt(t.style.width),n=t.getBoundingClientRect().right||this.$dmContainer.getBoundingClientRect().right+e;return this.$dmContainer.getBoundingClientRect().right-n},clearTimer(){clearInterval(this.timer),this.timer=null},initSuspendEvents(){let t=[];this.$refs.dmContainer.addEventListener("mousemove",e=>{let n=e.target;n.className.includes("dm")||(n=n.closest(".dm")||e.target),n.className.includes("dm")&&(n.classList.add("pause"),t.push(n))}),this.$refs.dmContainer.addEventListener("mouseout",e=>{let n=e.target;n.className.includes("dm")||(n=n.closest(".dm")||e.target),n.className.includes("dm")&&(n.classList.remove("pause"),t.forEach(t=>{t.classList.remove("pause")}),t=[])})},clear(){this.clearTimer(),this.index=0},reset(){this.$container=null,this.$dmContainer=null,this.danmuHeight=0,this.init()},stop(){this.danChannel={},this.$dmContainer.innerHTML="",this.paused=!0,this.hidden=!1,this.clear(),this.initDanmuList()},pause(){this.paused=!0},add(t){if(this.index===this.danmuList.length)return this.danmuList.push(t),this.danmuList.length-1;{const e=this.index%this.danmuList.length;return this.danmuList.splice(e,0,t),e+1}},push(t){return this.danmuList.push(t),this.danmuList.length-1},getPlayState(){return!this.paused},show(){this.hidden=!1},hide(){this.hidden=!0},resize(){this.initCore();const t=this.$dmContainer.getElementsByClassName("dm");for(let e=0;e<t.length;e++){const n=t[e];n.style.setProperty("--dm-scroll-width",`-${this.container.width+n.offsetWidth}px`),n.style.left=this.container.width+"px",n.style.animationDuration=this.container.width/this.danmu.speeds+"s"}}}},d=h;function l(t,e,n,r,i,a,o,s){var u,h="function"===typeof t?t.options:t;if(e&&(h.render=e,h.staticRenderFns=n,h._compiled=!0),r&&(h.functional=!0),a&&(h._scopeId="data-v-"+a),o?(u=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(o)},h._ssrRegister=u):i&&(u=s?function(){i.call(this,(h.functional?this.parent:this).$root.$options.shadowRoot)}:i),u)if(h.functional){h._injectStyles=u;var d=h.render;h.render=function(t,e){return u.call(e),d(t,e)}}else{var l=h.beforeCreate;h.beforeCreate=l?[].concat(l,u):[u]}return{exports:t,options:h}}n("a57b");var c=l(d,a,o,!1,null,null,null),f=c.exports;f.install=function(t){t.component("vueDanmaku",f)},"undefined"!==typeof window&&window.Vue&&window.Vue.use(f);var m=f;e["default"]=m},"378c":function(t,e,n){var r=n("a8d8");e=r(!1),e.push([t.i,".vue-danmaku{position:relative;overflow:hidden}.vue-danmaku .danmus{position:absolute;left:0;top:0;width:100%;height:100%;opacity:0;transition:all .3s}.vue-danmaku .danmus.show{opacity:1}.vue-danmaku .danmus.paused .dm.move{animation-play-state:paused}.vue-danmaku .danmus .dm{position:absolute;font-size:20px;color:#ddd;white-space:pre;transform:translateX(0);transform-style:preserve-3d}.vue-danmaku .danmus .dm.move{will-change:transform;animation-name:moveLeft;animation-timing-function:linear;animation-play-state:running}.vue-danmaku .danmus .dm.pause{animation-play-state:paused;z-index:10}@keyframes moveLeft{0%{transform:translateX(0)}to{transform:translateX(var(--dm-scroll-width))}}",""]),t.exports=e},"8bbf":function(e,n){e.exports=t},"8d94":function(t,e,n){var r=n("378c");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var i=n("c69b").default;i("f67b2c78",r,!0,{sourceMap:!1,shadowMode:!1})},a57b:function(t,e,n){"use strict";n("8d94")},a8d8:function(t,e,n){"use strict";function r(t,e){var n=t[1]||"",r=t[3];if(!r)return n;if(e&&"function"===typeof btoa){var a=i(r),o=r.sources.map((function(t){return"/*# sourceURL=".concat(r.sourceRoot||"").concat(t," */")}));return[n].concat(o).concat([a]).join("\n")}return[n].join("\n")}function i(t){var e=btoa(unescape(encodeURIComponent(JSON.stringify(t)))),n="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(e);return"/*# ".concat(n," */")}t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n=r(e,t);return e[2]?"@media ".concat(e[2]," {").concat(n,"}"):n})).join("")},e.i=function(t,n,r){"string"===typeof t&&(t=[[null,t,""]]);var i={};if(r)for(var a=0;a<this.length;a++){var o=this[a][0];null!=o&&(i[o]=!0)}for(var s=0;s<t.length;s++){var u=[].concat(t[s]);r&&i[u[0]]||(n&&(u[2]?u[2]="".concat(n," and ").concat(u[2]):u[2]=n),e.push(u))}},e}},c69b:function(t,e,n){"use strict";function r(t,e){for(var n=[],r={},i=0;i<e.length;i++){var a=e[i],o=a[0],s=a[1],u=a[2],h=a[3],d={id:t+":"+i,css:s,media:u,sourceMap:h};r[o]?r[o].parts.push(d):n.push(r[o]={id:o,parts:[d]})}return n}n.r(e),n.d(e,"default",(function(){return m}));var i="undefined"!==typeof document;if("undefined"!==typeof DEBUG&&DEBUG&&!i)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var a={},o=i&&(document.head||document.getElementsByTagName("head")[0]),s=null,u=0,h=!1,d=function(){},l=null,c="data-vue-ssr-id",f="undefined"!==typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function m(t,e,n,i){h=n,l=i||{};var o=r(t,e);return g(o),function(e){for(var n=[],i=0;i<o.length;i++){var s=o[i],u=a[s.id];u.refs--,n.push(u)}for(e?(o=r(t,e),g(o)):o=[],i=0;i<n.length;i++)if(u=n[i],0===u.refs){for(var h=0;h<u.parts.length;h++)u.parts[h]();delete a[u.id]}}}function g(t){for(var e=0;e<t.length;e++){var n=t[e],r=a[n.id];if(r){r.refs++;for(var i=0;i<r.parts.length;i++)r.parts[i](n.parts[i]);for(;i<n.parts.length;i++)r.parts.push(v(n.parts[i]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{var o=[];for(i=0;i<n.parts.length;i++)o.push(v(n.parts[i]));a[n.id]={id:n.id,refs:1,parts:o}}}}function p(){var t=document.createElement("style");return t.type="text/css",o.appendChild(t),t}function v(t){var e,n,r=document.querySelector("style["+c+'~="'+t.id+'"]');if(r){if(h)return d;r.parentNode.removeChild(r)}if(f){var i=u++;r=s||(s=p()),e=y.bind(null,r,i,!1),n=y.bind(null,r,i,!0)}else r=p(),e=w.bind(null,r),n=function(){r.parentNode.removeChild(r)};return e(t),function(r){if(r){if(r.css===t.css&&r.media===t.media&&r.sourceMap===t.sourceMap)return;e(t=r)}else n()}}var C=function(){var t=[];return function(e,n){return t[e]=n,t.filter(Boolean).join("\n")}}();function y(t,e,n,r){var i=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=C(e,i);else{var a=document.createTextNode(i),o=t.childNodes;o[e]&&t.removeChild(o[e]),o.length?t.insertBefore(a,o[e]):t.appendChild(a)}}function w(t,e){var n=e.css,r=e.media,i=e.sourceMap;if(r&&t.setAttribute("media",r),l.ssrId&&t.setAttribute(c,e.id),i&&(n+="\n/*# sourceURL="+i.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),t.styleSheet)t.styleSheet.cssText=n;else{while(t.firstChild)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}}})["default"]}))},"4d90":function(t,e,n){"use strict";var r=n("23e7"),i=n("0ccb").start,a=n("9a0c");r({target:"String",proto:!0,forced:a},{padStart:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},"4e82":function(t,e,n){"use strict";var r=n("23e7"),i=n("e330"),a=n("59ed"),o=n("7b0b"),s=n("07fa"),u=n("577e"),h=n("d039"),d=n("addb"),l=n("a640"),c=n("04d1"),f=n("d998"),m=n("2d00"),g=n("512ce"),p=[],v=i(p.sort),C=i(p.push),y=h((function(){p.sort(void 0)})),w=h((function(){p.sort(null)})),E=l("sort"),b=!h((function(){if(m)return m<70;if(!(c&&c>3)){if(f)return!0;if(g)return g<603;var t,e,n,r,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)p.push({k:e+r,v:n})}for(p.sort((function(t,e){return e.v-t.v})),r=0;r<p.length;r++)e=p[r].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),L=y||!w||!E||!b,T=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:u(e)>u(n)?1:-1}};r({target:"Array",proto:!0,forced:L},{sort:function(t){void 0!==t&&a(t);var e=o(this);if(b)return void 0===t?v(e):v(e,t);var n,r,i=[],u=s(e);for(r=0;r<u;r++)r in e&&C(i,e[r]);d(i,T(t)),n=i.length,r=0;while(r<n)e[r]=i[r++];while(r<u)delete e[r++];return e}})},"512ce":function(t,e,n){var r=n("342f"),i=r.match(/AppleWebKit\/(\d+)\./);t.exports=!!i&&+i[1]},"8a79":function(t,e,n){"use strict";var r=n("23e7"),i=n("e330"),a=n("06cf").f,o=n("50c4"),s=n("577e"),u=n("5a34"),h=n("1d80"),d=n("ab13"),l=n("c430"),c=i("".endsWith),f=i("".slice),m=Math.min,g=d("endsWith"),p=!l&&!g&&!!function(){var t=a(String.prototype,"endsWith");return t&&!t.writable}();r({target:"String",proto:!0,forced:!p&&!g},{endsWith:function(t){var e=s(h(this));u(t);var n=arguments.length>1?arguments[1]:void 0,r=e.length,i=void 0===n?r:m(o(n),r),a=s(t);return c?c(e,a,i):f(e,i-a.length,i)===a}})},"9a0c":function(t,e,n){var r=n("342f");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(r)},"9d4a":function(t,e,n){"use strict";var r=n("23e7"),i=n("da84"),a=n("2266"),o=n("59ed"),s=n("825a"),u=i.TypeError;r({target:"Iterator",proto:!0,real:!0},{reduce:function(t){s(this),o(t);var e=arguments.length<2,n=e?void 0:arguments[1];if(a(this,(function(r){e?(e=!1,n=r):n=t(n,r)}),{IS_ITERATOR:!0}),e)throw u("Reduce of empty iterator with no initial value");return n}})},d58f:function(t,e,n){var r=n("da84"),i=n("59ed"),a=n("7b0b"),o=n("44ad"),s=n("07fa"),u=r.TypeError,h=function(t){return function(e,n,r,h){i(n);var d=a(e),l=o(d),c=s(d),f=t?c-1:0,m=t?-1:1;if(r<2)while(1){if(f in l){h=l[f],f+=m;break}if(f+=m,t?f<0:c<=f)throw u("Reduce of empty array with no initial value")}for(;t?f>=0:c>f;f+=m)f in l&&(h=n(h,l[f],f,d));return h}};t.exports={left:h(!1),right:h(!0)}},d7b0:function(t,e,n){"use strict";
/*!
 * qrcode.vue v1.7.0
 * A Vue component to generate QRCode.
 * © 2017-2019 @scopewu(https://github.com/scopewu)
 * MIT License.
 */var r={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8};function i(t){this.mode=r.MODE_8BIT_BYTE,this.data=t}i.prototype={getLength:function(t){return this.data.length},write:function(t){for(var e=0;e<this.data.length;e++)t.put(this.data.charCodeAt(e),8)}};var a=i,o={L:1,M:0,Q:3,H:2};function s(t,e){this.totalCount=t,this.dataCount=e}s.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],s.getRSBlocks=function(t,e){var n=s.getRsBlockTable(t,e);if(void 0==n)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+e);for(var r=n.length/3,i=new Array,a=0;a<r;a++)for(var o=n[3*a+0],u=n[3*a+1],h=n[3*a+2],d=0;d<o;d++)i.push(new s(u,h));return i},s.getRsBlockTable=function(t,e){switch(e){case o.L:return s.RS_BLOCK_TABLE[4*(t-1)+0];case o.M:return s.RS_BLOCK_TABLE[4*(t-1)+1];case o.Q:return s.RS_BLOCK_TABLE[4*(t-1)+2];case o.H:return s.RS_BLOCK_TABLE[4*(t-1)+3];default:return}};var u=s;function h(){this.buffer=new Array,this.length=0}h.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};for(var d=h,l={glog:function(t){if(t<1)throw new Error("glog("+t+")");return l.LOG_TABLE[t]},gexp:function(t){while(t<0)t+=255;while(t>=256)t-=255;return l.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},c=0;c<8;c++)l.EXP_TABLE[c]=1<<c;for(c=8;c<256;c++)l.EXP_TABLE[c]=l.EXP_TABLE[c-4]^l.EXP_TABLE[c-5]^l.EXP_TABLE[c-6]^l.EXP_TABLE[c-8];for(c=0;c<255;c++)l.LOG_TABLE[l.EXP_TABLE[c]]=c;var f=l;function m(t,e){if(void 0==t.length)throw new Error(t.length+"/"+e);var n=0;while(n<t.length&&0==t[n])n++;this.num=new Array(t.length-n+e);for(var r=0;r<t.length-n;r++)this.num[r]=t[r+n]}m.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),n=0;n<this.getLength();n++)for(var r=0;r<t.getLength();r++)e[n+r]^=f.gexp(f.glog(this.get(n))+f.glog(t.get(r)));return new m(e,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var e=f.glog(this.get(0))-f.glog(t.get(0)),n=new Array(this.getLength()),r=0;r<this.getLength();r++)n[r]=this.get(r);for(r=0;r<t.getLength();r++)n[r]^=f.gexp(f.glog(t.get(r))+e);return new m(n,0).mod(t)}};var g=m,p={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},v={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){var e=t<<10;while(v.getBCHDigit(e)-v.getBCHDigit(v.G15)>=0)e^=v.G15<<v.getBCHDigit(e)-v.getBCHDigit(v.G15);return(t<<10|e)^v.G15_MASK},getBCHTypeNumber:function(t){var e=t<<12;while(v.getBCHDigit(e)-v.getBCHDigit(v.G18)>=0)e^=v.G18<<v.getBCHDigit(e)-v.getBCHDigit(v.G18);return t<<12|e},getBCHDigit:function(t){var e=0;while(0!=t)e++,t>>>=1;return e},getPatternPosition:function(t){return v.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,n){switch(t){case p.PATTERN000:return(e+n)%2==0;case p.PATTERN001:return e%2==0;case p.PATTERN010:return n%3==0;case p.PATTERN011:return(e+n)%3==0;case p.PATTERN100:return(Math.floor(e/2)+Math.floor(n/3))%2==0;case p.PATTERN101:return e*n%2+e*n%3==0;case p.PATTERN110:return(e*n%2+e*n%3)%2==0;case p.PATTERN111:return(e*n%3+(e+n)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new g([1],0),n=0;n<t;n++)e=e.multiply(new g([1,f.gexp(n)],0));return e},getLengthInBits:function(t,e){if(1<=e&&e<10)switch(t){case r.MODE_NUMBER:return 10;case r.MODE_ALPHA_NUM:return 9;case r.MODE_8BIT_BYTE:return 8;case r.MODE_KANJI:return 8;default:throw new Error("mode:"+t)}else if(e<27)switch(t){case r.MODE_NUMBER:return 12;case r.MODE_ALPHA_NUM:return 11;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 10;default:throw new Error("mode:"+t)}else{if(!(e<41))throw new Error("type:"+e);switch(t){case r.MODE_NUMBER:return 14;case r.MODE_ALPHA_NUM:return 13;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 12;default:throw new Error("mode:"+t)}}},getLostPoint:function(t){for(var e=t.getModuleCount(),n=0,r=0;r<e;r++)for(var i=0;i<e;i++){for(var a=0,o=t.isDark(r,i),s=-1;s<=1;s++)if(!(r+s<0||e<=r+s))for(var u=-1;u<=1;u++)i+u<0||e<=i+u||0==s&&0==u||o==t.isDark(r+s,i+u)&&a++;a>5&&(n+=3+a-5)}for(r=0;r<e-1;r++)for(i=0;i<e-1;i++){var h=0;t.isDark(r,i)&&h++,t.isDark(r+1,i)&&h++,t.isDark(r,i+1)&&h++,t.isDark(r+1,i+1)&&h++,0!=h&&4!=h||(n+=3)}for(r=0;r<e;r++)for(i=0;i<e-6;i++)t.isDark(r,i)&&!t.isDark(r,i+1)&&t.isDark(r,i+2)&&t.isDark(r,i+3)&&t.isDark(r,i+4)&&!t.isDark(r,i+5)&&t.isDark(r,i+6)&&(n+=40);for(i=0;i<e;i++)for(r=0;r<e-6;r++)t.isDark(r,i)&&!t.isDark(r+1,i)&&t.isDark(r+2,i)&&t.isDark(r+3,i)&&t.isDark(r+4,i)&&!t.isDark(r+5,i)&&t.isDark(r+6,i)&&(n+=40);var d=0;for(i=0;i<e;i++)for(r=0;r<e;r++)t.isDark(r,i)&&d++;var l=Math.abs(100*d/e/e-50)/5;return n+=10*l,n}},C=v;function y(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}var w=y.prototype;w.addData=function(t){var e=new a(t);this.dataList.push(e),this.dataCache=null},w.isDark=function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(t+","+e);return this.modules[t][e]},w.getModuleCount=function(){return this.moduleCount},w.make=function(){if(this.typeNumber<1){var t=1;for(t=1;t<40;t++){for(var e=u.getRSBlocks(t,this.errorCorrectLevel),n=new d,r=0,i=0;i<e.length;i++)r+=e[i].dataCount;for(i=0;i<this.dataList.length;i++){var a=this.dataList[i];n.put(a.mode,4),n.put(a.getLength(),C.getLengthInBits(a.mode,t)),a.write(n)}if(n.getLengthInBits()<=8*r)break}this.typeNumber=t}this.makeImpl(!1,this.getBestMaskPattern())},w.makeImpl=function(t,e){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++){this.modules[n]=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++)this.modules[n][r]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,e),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=y.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,e)},w.setupPositionProbePattern=function(t,e){for(var n=-1;n<=7;n++)if(!(t+n<=-1||this.moduleCount<=t+n))for(var r=-1;r<=7;r++)e+r<=-1||this.moduleCount<=e+r||(this.modules[t+n][e+r]=0<=n&&n<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==n||6==n)||2<=n&&n<=4&&2<=r&&r<=4)},w.getBestMaskPattern=function(){for(var t=0,e=0,n=0;n<8;n++){this.makeImpl(!0,n);var r=C.getLostPoint(this);(0==n||t>r)&&(t=r,e=n)}return e},w.createMovieClip=function(t,e,n){var r=t.createEmptyMovieClip(e,n),i=1;this.make();for(var a=0;a<this.modules.length;a++)for(var o=a*i,s=0;s<this.modules[a].length;s++){var u=s*i,h=this.modules[a][s];h&&(r.beginFill(0,100),r.moveTo(u,o),r.lineTo(u+i,o),r.lineTo(u+i,o+i),r.lineTo(u,o+i),r.endFill())}return r},w.setupTimingPattern=function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},w.setupPositionAdjustPattern=function(){for(var t=C.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var n=0;n<t.length;n++){var r=t[e],i=t[n];if(null==this.modules[r][i])for(var a=-2;a<=2;a++)for(var o=-2;o<=2;o++)this.modules[r+a][i+o]=-2==a||2==a||-2==o||2==o||0==a&&0==o}},w.setupTypeNumber=function(t){for(var e=C.getBCHTypeNumber(this.typeNumber),n=0;n<18;n++){var r=!t&&1==(e>>n&1);this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=r}for(n=0;n<18;n++){r=!t&&1==(e>>n&1);this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=r}},w.setupTypeInfo=function(t,e){for(var n=this.errorCorrectLevel<<3|e,r=C.getBCHTypeInfo(n),i=0;i<15;i++){var a=!t&&1==(r>>i&1);i<6?this.modules[i][8]=a:i<8?this.modules[i+1][8]=a:this.modules[this.moduleCount-15+i][8]=a}for(i=0;i<15;i++){a=!t&&1==(r>>i&1);i<8?this.modules[8][this.moduleCount-i-1]=a:i<9?this.modules[8][15-i-1+1]=a:this.modules[8][15-i-1]=a}this.modules[this.moduleCount-8][8]=!t},w.mapData=function(t,e){for(var n=-1,r=this.moduleCount-1,i=7,a=0,o=this.moduleCount-1;o>0;o-=2){6==o&&o--;while(1){for(var s=0;s<2;s++)if(null==this.modules[r][o-s]){var u=!1;a<t.length&&(u=1==(t[a]>>>i&1));var h=C.getMask(e,r,o-s);h&&(u=!u),this.modules[r][o-s]=u,i--,-1==i&&(a++,i=7)}if(r+=n,r<0||this.moduleCount<=r){r-=n,n=-n;break}}}},y.PAD0=236,y.PAD1=17,y.createData=function(t,e,n){for(var r=u.getRSBlocks(t,e),i=new d,a=0;a<n.length;a++){var o=n[a];i.put(o.mode,4),i.put(o.getLength(),C.getLengthInBits(o.mode,t)),o.write(i)}var s=0;for(a=0;a<r.length;a++)s+=r[a].dataCount;if(i.getLengthInBits()>8*s)throw new Error("code length overflow. ("+i.getLengthInBits()+">"+8*s+")");i.getLengthInBits()+4<=8*s&&i.put(0,4);while(i.getLengthInBits()%8!=0)i.putBit(!1);while(1){if(i.getLengthInBits()>=8*s)break;if(i.put(y.PAD0,8),i.getLengthInBits()>=8*s)break;i.put(y.PAD1,8)}return y.createBytes(i,r)},y.createBytes=function(t,e){for(var n=0,r=0,i=0,a=new Array(e.length),o=new Array(e.length),s=0;s<e.length;s++){var u=e[s].dataCount,h=e[s].totalCount-u;r=Math.max(r,u),i=Math.max(i,h),a[s]=new Array(u);for(var d=0;d<a[s].length;d++)a[s][d]=255&t.buffer[d+n];n+=u;var l=C.getErrorCorrectPolynomial(h),c=new g(a[s],l.getLength()-1),f=c.mod(l);o[s]=new Array(l.getLength()-1);for(d=0;d<o[s].length;d++){var m=d+f.getLength()-o[s].length;o[s][d]=m>=0?f.get(m):0}}var p=0;for(d=0;d<e.length;d++)p+=e[d].totalCount;var v=new Array(p),y=0;for(d=0;d<r;d++)for(s=0;s<e.length;s++)d<a[s].length&&(v[y++]=a[s][d]);for(d=0;d<i;d++)for(s=0;s<e.length;s++)d<o[s].length&&(v[y++]=o[s][d]);return v};var E=y;function b(t){for(var e="",n=0;n<t.length;n++){var r=t.charCodeAt(n);r<128?e+=String.fromCharCode(r):r<2048?(e+=String.fromCharCode(192|r>>6),e+=String.fromCharCode(128|63&r)):r<55296||r>=57344?(e+=String.fromCharCode(224|r>>12),e+=String.fromCharCode(128|r>>6&63),e+=String.fromCharCode(128|63&r)):(n++,r=65536+((1023&r)<<10|1023&t.charCodeAt(n)),e+=String.fromCharCode(240|r>>18),e+=String.fromCharCode(128|r>>12&63),e+=String.fromCharCode(128|r>>6&63),e+=String.fromCharCode(128|63&r))}return e}function L(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=[];return t.forEach((function(t,r){var i=null;t.forEach((function(a,o){if(!a&&null!==i)return n.push("M".concat(i+e," ").concat(r+e,"h").concat(o-i,"v1H").concat(i+e,"z")),void(i=null);if(o!==t.length-1)a&&null===i&&(i=o);else{if(!a)return;null===i?n.push("M".concat(o+e,",").concat(r+e," h1v1H").concat(o+e,"z")):n.push("M".concat(i+e,",").concat(r+e," h").concat(o+1-i,"v1H").concat(i+e,"z"))}}))})),n.join("")}var T={props:{value:{type:String,required:!0,default:""},className:{type:String,default:""},size:{type:[Number,String],default:100,validator:function(t){return!0!==isNaN(Number(t))}},level:{type:String,default:"L",validator:function(t){return["L","Q","M","H"].indexOf(t)>-1}},background:{type:String,default:"#fff"},foreground:{type:String,default:"#000"},renderAs:{type:String,required:!1,default:"canvas",validator:function(t){return["canvas","svg"].indexOf(t)>-1}}},data:function(){return{numCells:0,fgPath:""}},updated:function(){this.render()},mounted:function(){this.render()},methods:{render:function(){var t=this.value,e=this.size,n=this.level,r=this.background,i=this.foreground,a=this.renderAs,s=e>>>0,u=new E(-1,o[n]);u.addData(b(t)),u.make();var h=u.modules,d=s/h.length,l=s/h.length,c=window.devicePixelRatio||1;if("svg"===a)this.numCells=h.length,this.fgPath=L(h);else{var f=this.$refs["qrcode-vue"],m=f.getContext("2d");f.height=f.width=s*c,m.scale(c,c),h.forEach((function(t,e){t.forEach((function(t,n){m.fillStyle=t?i:r;var a=Math.ceil((n+1)*d)-Math.floor(n*d),o=Math.ceil((e+1)*l)-Math.floor(e*l);m.fillRect(Math.round(n*d),Math.round(e*l),a,o)}))}))}}},render:function(t){var e=this.className,n=this.value,r=this.level,i=this.background,a=this.foreground,o=this.size,s=this.renderAs,u=this.numCells,h=this.fgPath;return t("div",{class:this.class||e,attrs:{value:n,level:r,background:i,foreground:a}},["svg"===s?t("svg",{attrs:{height:o,width:o,shapeRendering:"crispEdges",viewBox:"0 0 ".concat(u," ").concat(u)},style:{width:o+"px",height:o+"px"}},[t("path",{attrs:{fill:i,d:"M0,0 h".concat(u,"v").concat(u,"H0z")}}),t("path",{attrs:{fill:a,d:h}})]):t("canvas",{attrs:{height:o,width:o},style:{width:o+"px",height:o+"px"},ref:"qrcode-vue"},[])])}};e["a"]=T},d998:function(t,e,n){var r=n("342f");t.exports=/MSIE|Trident/.test(r)}}]);